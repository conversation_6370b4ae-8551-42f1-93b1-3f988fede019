import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import '../../app_properties.dart';
import '../../models/order.dart';
import '../../services/data_service.dart';
import '../../services/app_state.dart';
import '../../services/firestore_data_service.dart';

class OrderHistoryPage extends StatefulWidget {
  const OrderHistoryPage({super.key});

  @override
  State<OrderHistoryPage> createState() => _OrderHistoryPageState();
}

class _OrderHistoryPageState extends State<OrderHistoryPage>
    with TickerProviderStateMixin, WidgetsBindingObserver {
  late TabController _tabController;
  List<Order> _orders = [];
  bool _isLoading = true;
  final AppState _appState = AppState();

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    WidgetsBinding.instance.addObserver(this);
    _loadOrders();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    if (state == AppLifecycleState.resumed) {
      // إعادة تحميل البيانات عند العودة للتطبيق
      _loadOrders();
    }
  }

  Future<void> _loadOrders() async {
    try {
      // تحميل الطلبات الحقيقية للمستخدم الحالي
      await Future.delayed(const Duration(seconds: 1));

      final currentUser = _appState.currentUser;

      if (currentUser != null) {
        // جلب الطلبات من Firestore أولاً
        List<Map<String, dynamic>> ordersData = [];
        try {
          ordersData = await FirestoreDataService.getOrders();
          if (kDebugMode) {
            print('✅ تم جلب ${ordersData.length} طلب من Firestore');
          }
        } catch (e) {
          if (kDebugMode) {
            print('⚠️ فشل في جلب الطلبات من Firestore: $e');
            print('🔄 التبديل للبيانات المحلية...');
          }
          // في حالة فشل Firestore، استخدم البيانات المحلية
          ordersData = await DataService.getOrders();
        }

        // فلترة الطلبات للمستخدم الحالي
        if (kDebugMode) {
          print('🔍 فلترة الطلبات للمستخدم: ${currentUser.email} (ID: ${currentUser.id})');
          print('📦 إجمالي الطلبات قبل الفلترة: ${ordersData.length}');
          for (var order in ordersData) {
            print(
              '   طلب: ${order['id']} - العميل: ${order['customerEmail']} - المستخدم: ${order['userId']}',
            );
          }
        }

        final userOrders = ordersData
            .where(
              (orderData) {
                final orderUserId = orderData['userId']?.toString() ?? '';
                final orderEmail = orderData['customerEmail']?.toString().toLowerCase() ?? '';
                final currentUserId = currentUser.id;
                final currentEmail = currentUser.email.toLowerCase();

                final matchesUserId = orderUserId == currentUserId;
                final matchesEmail = orderEmail == currentEmail;

                if (kDebugMode) {
                  print('🔍 مقارنة الطلب ${orderData['id']}:');
                  print('   - orderUserId: "$orderUserId" vs currentUserId: "$currentUserId" = $matchesUserId');
                  print('   - orderEmail: "$orderEmail" vs currentEmail: "$currentEmail" = $matchesEmail');
                }

                return matchesUserId || matchesEmail;
              },
            )
            .toList();

        if (kDebugMode) {
          print('📦 الطلبات بعد الفلترة: ${userOrders.length}');
          for (var order in userOrders) {
            print('✅ طلب مطابق: ${order['id']} - ${order['status']}');
          }
        }

        setState(() {
          _orders = userOrders.map((orderData) {
            return Order(
              id: orderData['id'] ?? '',
              date:
                  DateTime.tryParse(
                    orderData['createdAt'] ?? orderData['orderDate'] ?? '',
                  ) ??
                  DateTime.now(),
              status: _parseOrderStatus(orderData['status'] ?? 'في الانتظار'),
              total: (orderData['total'] ?? 0).toDouble(),
              items: (orderData['items'] as List<dynamic>? ?? []).map((
                itemData,
              ) {
                return OrderItem(
                  productId: itemData['productId'] ?? '',
                  productName: itemData['name'] ?? '',
                  quantity: itemData['quantity'] ?? 1,
                  price: (itemData['price'] ?? 0).toDouble(),
                  imageUrl: '',
                  selectedColor: itemData['selectedColor'],
                  selectedSize: itemData['selectedSize'],
                );
              }).toList(),
              shippingAddress: orderData['address'] ?? '',
              notes: orderData['notes'],
            );
          }).toList();
          _isLoading = false;
        });
      } else {
        setState(() {
          _orders = [];
          _isLoading = false;
        });
      }
    } catch (e) {
      if (kDebugMode) print('خطأ في تحميل الطلبات: $e');
      setState(() {
        _orders = [];
        _isLoading = false;
      });
    }
  }

  OrderStatus _parseOrderStatus(String status) {
    final statusLower = status.toLowerCase().trim();

    if (kDebugMode) {
      print('🔍 تحويل حالة الطلب: "$status" -> "$statusLower"');
    }

    switch (statusLower) {
      case 'pending':
      case 'في الانتظار':
      case 'قيد المراجعة':
        return OrderStatus.processing;
      case 'confirmed':
      case 'تم التأكيد':
      case 'مؤكد':
        return OrderStatus.confirmed;
      case 'processing':
      case 'قيد التحضير':
      case 'قيد التجهيز':
        return OrderStatus.preparing;
      case 'shipped':
      case 'تم الشحن':
      case 'مشحون':
        return OrderStatus.shipped;
      case 'delivered':
      case 'تم التسليم':
      case 'مسلم':
        return OrderStatus.delivered;
      case 'cancelled':
      case 'ملغي':
      case 'ملغى':
        return OrderStatus.cancelled;
      default:
        if (kDebugMode) {
          print('⚠️ حالة طلب غير معروفة: "$status" - استخدام الافتراضي');
        }
        return OrderStatus.processing;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.scaffoldBackground,
      appBar: AppBar(
        title: const Text('تاريخ الطلبات'),
        backgroundColor: AppColors.white,
        foregroundColor: AppColors.primaryText,
        elevation: 1,
        centerTitle: true,
        bottom: TabBar(
          controller: _tabController,
          labelColor: AppColors.primaryColor,
          unselectedLabelColor: AppColors.secondaryText,
          indicatorColor: AppColors.primaryColor,
          tabs: const [
            Tab(text: 'الكل'),
            Tab(text: 'قيد المعالجة'),
            Tab(text: 'تم الشحن'),
            Tab(text: 'تم التسليم'),
          ],
        ),
      ),
      body: _isLoading
          ? _buildLoadingState()
          : RefreshIndicator(onRefresh: _loadOrders, child: _buildOrdersList()),
    );
  }

  Widget _buildLoadingState() {
    return const Center(child: CircularProgressIndicator());
  }

  Widget _buildOrdersList() {
    if (_orders.isEmpty) {
      return _buildEmptyState();
    }

    return TabBarView(
      controller: _tabController,
      children: [
        _buildOrdersTab(_orders),
        _buildOrdersTab(
          _orders.where((o) => o.status == OrderStatus.processing).toList(),
        ),
        _buildOrdersTab(
          _orders.where((o) => o.status == OrderStatus.shipped).toList(),
        ),
        _buildOrdersTab(
          _orders.where((o) => o.status == OrderStatus.delivered).toList(),
        ),
      ],
    );
  }

  Widget _buildOrdersTab(List<Order> orders) {
    if (orders.isEmpty) {
      return _buildEmptyState();
    }

    return ListView.builder(
      padding: const EdgeInsets.all(AppDimensions.paddingMedium),
      itemCount: orders.length,
      itemBuilder: (context, index) {
        return _buildOrderCard(orders[index]);
      },
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            width: 120,
            height: 120,
            decoration: BoxDecoration(
              color: AppColors.primaryColor.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(60),
            ),
            child: const Icon(
              Icons.shopping_bag_outlined,
              size: 60,
              color: AppColors.primaryColor,
            ),
          ),
          const SizedBox(height: 24),
          Text(
            'لا توجد طلبات',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              fontWeight: FontWeight.bold,
              color: AppColors.primaryText,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'لم تقم بأي طلبات حتى الآن',
            style: Theme.of(
              context,
            ).textTheme.bodyLarge?.copyWith(color: AppColors.secondaryText),
          ),
          const SizedBox(height: 32),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('ابدأ التسوق'),
          ),
        ],
      ),
    );
  }

  Widget _buildOrderCard(Order order) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      decoration: BoxDecoration(
        color: AppColors.white,
        borderRadius: BorderRadius.circular(AppDimensions.borderRadiusLarge),
        boxShadow: [
          BoxShadow(
            color: AppColors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: InkWell(
        onTap: () => _showOrderDetails(order),
        borderRadius: BorderRadius.circular(AppDimensions.borderRadiusLarge),
        child: Padding(
          padding: const EdgeInsets.all(AppDimensions.paddingMedium),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // رأس الطلب
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'طلب #${order.id}',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  _buildStatusBadge(order.status),
                ],
              ),

              const SizedBox(height: 8),

              // تاريخ الطلب
              Text(
                'تاريخ الطلب: ${_formatDate(order.date)}',
                style: Theme.of(
                  context,
                ).textTheme.bodySmall?.copyWith(color: AppColors.secondaryText),
              ),

              const SizedBox(height: 12),

              // عناصر الطلب
              ...order.items.take(2).map((item) => _buildOrderItem(item)),

              if (order.items.length > 2) ...[
                const SizedBox(height: 8),
                Text(
                  'و ${order.items.length - 2} منتجات أخرى',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: AppColors.secondaryText,
                  ),
                ),
              ],

              const Divider(height: 24),

              // المجموع وأزرار العمليات
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'المجموع: ${AppConstants.formatPrice(order.total)}',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: AppColors.primaryColor,
                    ),
                  ),
                  Row(
                    children: [
                      if (order.status == OrderStatus.delivered)
                        TextButton(
                          onPressed: () => _reorderItems(order),
                          child: const Text('إعادة الطلب'),
                        ),
                      TextButton(
                        onPressed: () => _showOrderDetails(order),
                        child: const Text('التفاصيل'),
                      ),
                    ],
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildOrderItem(OrderItem item) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        children: [
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: AppColors.scaffoldBackground,
              borderRadius: BorderRadius.circular(8),
            ),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(8),
              child: Image.network(
                item.imageUrl,
                fit: BoxFit.cover,
                errorBuilder: (context, error, stackTrace) {
                  return const Icon(
                    Icons.image_not_supported,
                    color: AppColors.grey,
                    size: 20,
                  );
                },
              ),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  item.productName,
                  style: Theme.of(context).textTheme.bodyMedium,
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
                Text(
                  'الكمية: ${item.quantity} × ${AppConstants.formatPrice(item.price)}',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: AppColors.secondaryText,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatusBadge(OrderStatus status) {
    Color color;
    String text;

    switch (status) {
      case OrderStatus.processing:
        color = AppColors.warning;
        text = 'قيد المعالجة';
        break;
      case OrderStatus.confirmed:
        color = AppColors.primaryColor;
        text = 'مؤكد';
        break;
      case OrderStatus.preparing:
        color = AppColors.warning;
        text = 'قيد التحضير';
        break;
      case OrderStatus.shipped:
        color = AppColors.primaryColor;
        text = 'تم الشحن';
        break;
      case OrderStatus.delivered:
        color = AppColors.success;
        text = 'تم التسليم';
        break;
      case OrderStatus.cancelled:
        color = AppColors.errorColor;
        text = 'ملغي';
        break;
      case OrderStatus.returned:
        color = AppColors.errorColor;
        text = 'مرتجع';
        break;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
      decoration: BoxDecoration(
        color: color,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Text(
        text,
        style: const TextStyle(
          color: AppColors.white,
          fontSize: 12,
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  void _showOrderDetails(Order order) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => _buildOrderDetailsSheet(order),
    );
  }

  Widget _buildOrderDetailsSheet(Order order) {
    return Container(
      height: MediaQuery.of(context).size.height * 0.8,
      decoration: const BoxDecoration(
        color: AppColors.white,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(AppDimensions.borderRadiusLarge),
          topRight: Radius.circular(AppDimensions.borderRadiusLarge),
        ),
      ),
      child: Column(
        children: [
          // Handle
          Container(
            width: 40,
            height: 4,
            margin: const EdgeInsets.symmetric(vertical: 12),
            decoration: BoxDecoration(
              color: AppColors.grey,
              borderRadius: BorderRadius.circular(2),
            ),
          ),

          // Header
          Padding(
            padding: const EdgeInsets.all(AppDimensions.paddingMedium),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'تفاصيل الطلب #${order.id}',
                  style: Theme.of(
                    context,
                  ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
                ),
                IconButton(
                  onPressed: () => Navigator.of(context).pop(),
                  icon: const Icon(Icons.close),
                ),
              ],
            ),
          ),

          const Divider(),

          // Content
          Expanded(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(AppDimensions.paddingMedium),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // معلومات الطلب
                  _buildDetailSection('معلومات الطلب', [
                    _buildDetailRow('رقم الطلب', order.id),
                    _buildDetailRow('التاريخ', _formatDate(order.date)),
                    _buildDetailRow('الحالة', _getStatusText(order.status)),
                    _buildDetailRow(
                      'المجموع',
                      AppConstants.formatPrice(order.total),
                    ),
                  ]),

                  const SizedBox(height: 24),

                  // عناصر الطلب
                  _buildDetailSection(
                    'عناصر الطلب',
                    order.items
                        .map((item) => _buildDetailOrderItem(item))
                        .toList(),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDetailSection(String title, List<Widget> children) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.bold,
            color: AppColors.primaryColor,
          ),
        ),
        const SizedBox(height: 12),
        ...children,
      ],
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(label, style: Theme.of(context).textTheme.bodyMedium),
          Text(
            value,
            style: Theme.of(
              context,
            ).textTheme.bodyMedium?.copyWith(fontWeight: FontWeight.w500),
          ),
        ],
      ),
    );
  }

  Widget _buildDetailOrderItem(OrderItem item) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: AppColors.scaffoldBackground,
        borderRadius: BorderRadius.circular(AppDimensions.borderRadiusMedium),
      ),
      child: Row(
        children: [
          Container(
            width: 60,
            height: 60,
            decoration: BoxDecoration(
              color: AppColors.white,
              borderRadius: BorderRadius.circular(8),
            ),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(8),
              child: Image.network(
                item.imageUrl,
                fit: BoxFit.cover,
                errorBuilder: (context, error, stackTrace) {
                  return const Icon(
                    Icons.image_not_supported,
                    color: AppColors.grey,
                  );
                },
              ),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  item.productName,
                  style: Theme.of(
                    context,
                  ).textTheme.titleSmall?.copyWith(fontWeight: FontWeight.w600),
                ),
                const SizedBox(height: 4),
                Text(
                  'الكمية: ${item.quantity}',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: AppColors.secondaryText,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  AppConstants.formatPrice(item.price),
                  style: Theme.of(context).textTheme.titleSmall?.copyWith(
                    color: AppColors.primaryColor,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  String _getStatusText(OrderStatus status) {
    switch (status) {
      case OrderStatus.processing:
        return 'قيد المعالجة';
      case OrderStatus.confirmed:
        return 'مؤكد';
      case OrderStatus.preparing:
        return 'قيد التحضير';
      case OrderStatus.shipped:
        return 'تم الشحن';
      case OrderStatus.delivered:
        return 'تم التسليم';
      case OrderStatus.cancelled:
        return 'ملغي';
      case OrderStatus.returned:
        return 'مرتجع';
    }
  }

  void _reorderItems(Order order) {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('سيتم تنفيذ إعادة الطلب قريباً'),
        duration: Duration(seconds: 2),
      ),
    );
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    _tabController.dispose();
    super.dispose();
  }
}
