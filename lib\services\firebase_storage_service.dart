import 'dart:typed_data';
import 'dart:convert';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:flutter/foundation.dart';
import 'package:image/image.dart' as img;

/// خدمة Firebase Storage لرفع وإدارة الصور
class FirebaseStorageService {
  static late FirebaseStorage _storage;
  static bool _isInitialized = false;

  /// تهيئة الخدمة
  static Future<void> initialize() async {
    try {
      if (_isInitialized) return;

      if (kDebugMode) {
        print('🔄 تهيئة Firebase Storage Service...');
      }

      // تهيئة Firebase Storage مع bucket صحيح
      _storage = FirebaseStorage.instanceFor(
        bucket: 'visionlens-app-5ab70.firebasestorage.app'
      );

      _isInitialized = true;

      if (kDebugMode) {
        print('✅ تم تهيئة Firebase Storage بنجاح');
        print('🪣 Storage Bucket: visionlens-app-5ab70.firebasestorage.app');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في تهيئة Firebase Storage: $e');
      }
      rethrow;
    }
  }

  /// رفع صورة منتج إلى Firebase Storage
  static Future<String?> uploadProductImage(
    String productId,
    Uint8List imageBytes,
    String fileName,
  ) async {
    try {
      if (!_isInitialized) {
        await initialize();
      }

      if (kDebugMode) {
        print('🔄 رفع صورة المنتج: $fileName');
        print('📏 حجم الصورة: ${imageBytes.length} بايت');
      }

      // محاولة رفع إلى Firebase Storage أولاً
      try {
        // إنشاء مسار فريد للصورة
        final timestamp = DateTime.now().millisecondsSinceEpoch;
        final extension = fileName.split('.').last.toLowerCase();
        final storagePath = 'products/$productId/${timestamp}_$fileName';

        if (kDebugMode) {
          print('📁 مسار التخزين: $storagePath');
        }

        // رفع الصورة
        final ref = _storage.ref().child(storagePath);
        final uploadTask = ref.putData(
          imageBytes,
          SettableMetadata(
            contentType: _getContentType(extension),
            customMetadata: {
              'productId': productId,
              'uploadedAt': DateTime.now().toIso8601String(),
            },
          ),
        );

        // انتظار اكتمال الرفع
        final snapshot = await uploadTask;

        // الحصول على رابط التحميل
        final downloadUrl = await snapshot.ref.getDownloadURL();

        if (kDebugMode) {
          print('✅ تم رفع الصورة إلى Firebase Storage بنجاح');
          print('🔗 رابط التحميل: $downloadUrl');
        }

        return downloadUrl;
      } catch (storageError) {
        if (kDebugMode) {
          print('⚠️ فشل رفع Firebase Storage: $storageError');
          print('🔄 التبديل إلى Base64 كبديل...');
        }

        // في حالة فشل Firebase Storage، استخدم Base64
        final base64String = _convertToBase64(imageBytes, fileName);

        if (kDebugMode) {
          print('✅ تم تحويل الصورة إلى Base64 كبديل');
          print('📏 حجم Base64: ${base64String.length} حرف');
        }

        return base64String;
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في رفع صورة المنتج: $e');
      }
      return null;
    }
  }

  /// حذف صورة منتج من Firebase Storage
  static Future<bool> deleteProductImage(String imageUrl) async {
    try {
      if (!_isInitialized) {
        await initialize();
      }

      if (kDebugMode) {
        print('🗑️ حذف صورة المنتج: $imageUrl');
      }

      // استخراج المسار من الرابط
      final ref = _storage.refFromURL(imageUrl);
      await ref.delete();

      if (kDebugMode) {
        print('✅ تم حذف الصورة بنجاح');
      }

      return true;
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في حذف صورة المنتج: $e');
      }
      return false;
    }
  }

  /// حذف جميع صور منتج
  static Future<bool> deleteAllProductImages(String productId) async {
    try {
      if (!_isInitialized) {
        await initialize();
      }

      if (kDebugMode) {
        print('🗑️ حذف جميع صور المنتج: $productId');
      }

      final ref = _storage.ref().child('products/$productId');
      final listResult = await ref.listAll();

      // حذف جميع الملفات
      for (final item in listResult.items) {
        await item.delete();
      }

      if (kDebugMode) {
        print('✅ تم حذف ${listResult.items.length} صورة للمنتج $productId');
      }

      return true;
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في حذف صور المنتج: $e');
      }
      return false;
    }
  }

  /// الحصول على نوع المحتوى من الامتداد
  static String _getContentType(String extension) {
    switch (extension.toLowerCase()) {
      case 'png':
        return 'image/png';
      case 'jpg':
      case 'jpeg':
        return 'image/jpeg';
      case 'gif':
        return 'image/gif';
      case 'webp':
        return 'image/webp';
      default:
        return 'image/jpeg';
    }
  }

  /// رفع صور متعددة للمنتج
  static Future<List<String>> uploadMultipleProductImages(
    String productId,
    List<Uint8List> imagesBytes,
    List<String> fileNames,
  ) async {
    final uploadedUrls = <String>[];

    try {
      if (!_isInitialized) {
        await initialize();
      }

      if (kDebugMode) {
        print('🔄 رفع ${imagesBytes.length} صورة للمنتج: $productId');
      }

      for (int i = 0; i < imagesBytes.length; i++) {
        final imageBytes = imagesBytes[i];
        final fileName = fileNames[i];

        final url = await uploadProductImage(productId, imageBytes, fileName);
        if (url != null) {
          uploadedUrls.add(url);
        }
      }

      if (kDebugMode) {
        print('✅ تم رفع ${uploadedUrls.length} من ${imagesBytes.length} صورة');
      }

      return uploadedUrls;
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في رفع الصور المتعددة: $e');
      }
      return uploadedUrls;
    }
  }

  /// التحقق من صحة الصورة
  static bool isValidImage(Uint8List imageBytes, String fileName) {
    // التحقق من الحجم (أقل من 20MB للحفاظ على الجودة العالية)
    if (imageBytes.length > 20 * 1024 * 1024) {
      if (kDebugMode) {
        print('❌ الصورة كبيرة جداً: ${imageBytes.length} بايت');
      }
      return false;
    }

    // التحقق من الامتداد
    final extension = fileName.split('.').last.toLowerCase();
    final validExtensions = ['png', 'jpg', 'jpeg', 'gif', 'webp'];
    if (!validExtensions.contains(extension)) {
      if (kDebugMode) {
        print('❌ امتداد الصورة غير مدعوم: $extension');
      }
      return false;
    }

    return true;
  }

  /// تحويل الصورة إلى Base64 بجودة عالية مع ضغط ذكي
  static String _convertToBase64(Uint8List imageBytes, String fileName) {
    try {
      // تحديد نوع الصورة
      final extension = fileName.split('.').last.toLowerCase();
      String mimeType;

      switch (extension) {
        case 'jpg':
        case 'jpeg':
          mimeType = 'image/jpeg';
          break;
        case 'png':
          mimeType = 'image/png';
          break;
        case 'webp':
          mimeType = 'image/webp';
          break;
        default:
          mimeType = 'image/jpeg';
      }

      // ضغط ذكي للصور الكبيرة فقط
      Uint8List finalBytes = imageBytes;

      // إذا كانت الصورة أكبر من 1.5MB، قم بضغطها قليلاً
      if (imageBytes.length > 1.5 * 1024 * 1024) {
        try {
          // ضغط خفيف جداً للحفاظ على الجودة
          finalBytes = _compressImageLightly(imageBytes);

          if (kDebugMode) {
            print('🔄 تم ضغط الصورة من ${imageBytes.length} إلى ${finalBytes.length} بايت');
          }
        } catch (e) {
          if (kDebugMode) {
            print('⚠️ فشل الضغط، استخدام الصورة الأصلية: $e');
          }
          finalBytes = imageBytes;
        }
      }

      // تحويل إلى Base64
      final base64String = base64Encode(finalBytes);

      // التحقق من حد Firestore (1MB)
      final dataUrl = 'data:$mimeType;base64,$base64String';
      if (dataUrl.length > 1048487) {
        if (kDebugMode) {
          print('⚠️ الصورة لا تزال كبيرة (${dataUrl.length} حرف)، ضغط إضافي...');
        }
        // ضغط إضافي إذا لزم الأمر
        finalBytes = _compressImageForFirestore(imageBytes);
        final compressedBase64 = base64Encode(finalBytes);
        final compressedDataUrl = 'data:$mimeType;base64,$compressedBase64';

        if (kDebugMode) {
          print('✅ حجم نهائي: ${compressedDataUrl.length} حرف');
        }

        return compressedDataUrl;
      }

      // إرجاع Data URL كامل
      return dataUrl;
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في تحويل الصورة إلى Base64: $e');
      }
      // في حالة الخطأ، استخدم JPEG كافتراضي مع ضغط
      final compressedBytes = _compressImageForFirestore(imageBytes);
      final base64String = base64Encode(compressedBytes);
      return 'data:image/jpeg;base64,$base64String';
    }
  }

  /// ضغط خفيف للصورة باستخدام مكتبة image
  static Uint8List _compressImageLightly(Uint8List imageBytes) {
    try {
      // فك تشفير الصورة
      final image = img.decodeImage(imageBytes);
      if (image == null) {
        if (kDebugMode) {
          print('⚠️ فشل في فك تشفير الصورة، استخدام الأصلية');
        }
        return imageBytes;
      }

      // ضغط خفيف - جودة 85%
      final compressedBytes = img.encodeJpg(image, quality: 85);

      if (kDebugMode) {
        print('🔄 ضغط خفيف: ${imageBytes.length} → ${compressedBytes.length} بايت');
      }

      return Uint8List.fromList(compressedBytes);
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في الضغط الخفيف: $e');
      }
      return imageBytes;
    }
  }

  /// ضغط للتوافق مع Firestore
  static Uint8List _compressImageForFirestore(Uint8List imageBytes) {
    try {
      // فك تشفير الصورة
      final image = img.decodeImage(imageBytes);
      if (image == null) {
        if (kDebugMode) {
          print('⚠️ فشل في فك تشفير الصورة للضغط');
        }
        return imageBytes;
      }

      // حساب الحجم المطلوب (Base64 يزيد الحجم بـ 33%)
      // تقليل الحد أكثر لضمان أن الوثيقة الكاملة لا تتجاوز 1MB
      final maxBase64Size = 500000; // 500KB فقط للصورة
      final maxImageSize = (maxBase64Size * 0.75).round(); // تقريباً 375KB

      // ضغط تدريجي حتى نصل للحجم المطلوب
      int quality = 70;
      Uint8List compressedBytes;

      do {
        final compressed = img.encodeJpg(image, quality: quality);
        compressedBytes = Uint8List.fromList(compressed);

        if (kDebugMode) {
          print('🔄 ضغط بجودة $quality%: ${compressedBytes.length} بايت');
        }

        if (compressedBytes.length <= maxImageSize) {
          break;
        }

        quality -= 10;
      } while (quality > 20);

      if (kDebugMode) {
        print('✅ ضغط نهائي: ${imageBytes.length} → ${compressedBytes.length} بايت');
      }

      return compressedBytes;
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في ضغط Firestore: $e');
      }
      return imageBytes;
    }
  }
}
