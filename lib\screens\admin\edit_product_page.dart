import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'dart:convert';
import 'dart:async';
import 'package:image_picker/image_picker.dart';
import 'package:file_picker/file_picker.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

import '../../app_properties.dart';
import '../../models/product.dart';
import '../../models/category.dart' as category_model;
import '../../api_service_mock.dart';
import '../../services/firestore_data_service.dart';
import '../../services/firebase_platform_service.dart';
import '../../services/firebase_storage_service.dart';
import '../../services/image_processing_service.dart';

class EditProductPage extends StatefulWidget {
  final Product product;

  const EditProductPage({super.key, required this.product});

  @override
  State<EditProductPage> createState() => _EditProductPageState();
}

class _EditProductPageState extends State<EditProductPage> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _priceController = TextEditingController();
  final _stockController = TextEditingController();
  final _brandController = TextEditingController();
  final _skuController = TextEditingController();

  List<category_model.Category> _categories = [];
  String? _selectedCategoryId;
  bool _isLoading = false;
  bool _isFeatured = false;

  // متغيرات الصور
  String _existingMainImage = '';
  List<String> _existingAdditionalImages = [];
  Uint8List? _selectedImageBytes;
  String? _selectedImageName;
  String? _selectedImageType;

  // متغيرات البراندات
  List<Map<String, dynamic>> _brands = [];
  String? _selectedBrandId;
  bool _isLoadingBrands = false;

  @override
  void initState() {
    super.initState();
    _loadCategories();
    _loadBrands();
    _initializeFields();
  }

  @override
  void dispose() {
    _nameController.dispose();
    _descriptionController.dispose();
    _priceController.dispose();
    _stockController.dispose();
    _brandController.dispose();
    _skuController.dispose();
    super.dispose();
  }

  void _initializeFields() {
    _nameController.text = widget.product.name;
    _descriptionController.text = widget.product.description;
    _priceController.text = widget.product.price.toString();
    _stockController.text = widget.product.stockQuantity.toString();
    _brandController.text = widget.product.brand ?? '';
    _skuController.text = widget.product.tags ?? '';

    _selectedCategoryId = widget.product.categoryId;
    _isFeatured = widget.product.isFeatured;
    _existingMainImage = widget.product.image;
    _existingAdditionalImages = List.from(widget.product.images);

    // تحديد البراند المحدد إذا كان موجود
    if (widget.product.brand != null && widget.product.brand!.isNotEmpty) {
      _selectedBrandId = widget.product.brand;
    }

    if (kDebugMode) {
      debugPrint('🔍 تهيئة الحقول - categoryId: $_selectedCategoryId, brandId: $_selectedBrandId');
    }
  }

  Future<void> _loadCategories() async {
    try {
      final categories = await FirestoreDataService.getCategories();

      if (kDebugMode) {
        debugPrint('🔍 _loadCategories: categoryId قبل المعالجة: $_selectedCategoryId');
        debugPrint('🔍 _loadCategories: عدد الفئات المحملة: ${categories.length}');
        for (var cat in categories) {
          debugPrint('🔍 فئة: ${cat.name} (${cat.id})');
        }
      }

      setState(() {
        _categories = categories;
        // التأكد من أن الفئة المحددة موجودة في القائمة
        if (_selectedCategoryId != null) {
          // أولاً، تحقق من وجود الفئة بالمعرف
          bool categoryExists = _categories.any(
            (cat) => cat.id == _selectedCategoryId,
          );

          if (kDebugMode) {
            debugPrint('🔍 هل الفئة موجودة بالمعرف؟ $categoryExists');
          }

          // إذا لم توجد، تحقق من وجودها بالاسم (للبيانات القديمة)
          if (!categoryExists) {
            final originalCategoryId = _selectedCategoryId;
            final categoryByName = _categories.where(
              (cat) => cat.name == _selectedCategoryId,
            ).firstOrNull;

            if (kDebugMode) {
              debugPrint('🔍 البحث عن الفئة بالاسم "$_selectedCategoryId": ${categoryByName?.name} (${categoryByName?.id})');
            }

            if (categoryByName != null) {
              _selectedCategoryId = categoryByName.id;
              categoryExists = true;
              if (kDebugMode) {
                debugPrint('🔄 تم تحويل اسم الفئة "$originalCategoryId" إلى معرف: ${categoryByName.id}');
              }

              // تحديث المنتج في Firebase بالمعرف الصحيح
              _updateProductCategoryInFirebase(categoryByName.id);
            }
          }

          // إذا لم توجد الفئة، استخدم الأولى
          if (!categoryExists && _categories.isNotEmpty) {
            _selectedCategoryId = _categories.first.id;
            if (kDebugMode) {
              debugPrint('⚠️ الفئة غير موجودة، تم اختيار الفئة الأولى: $_selectedCategoryId');
            }
          }
        } else if (_categories.isNotEmpty) {
          _selectedCategoryId = _categories.first.id;
        }
      });
      if (kDebugMode) {
        debugPrint('✅ تم تحميل ${_categories.length} فئة');
        debugPrint('🔍 الفئة المحددة: $_selectedCategoryId');
      }
    } catch (e) {
      if (kDebugMode) debugPrint('❌ خطأ في تحميل الفئات: $e');
    }
  }

  Future<void> _loadBrands() async {
    setState(() {
      _isLoadingBrands = true;
    });

    try {
      final brands = await FirebasePlatformService.getBrands();

      if (kDebugMode) {
        debugPrint('🔍 _loadBrands: brandId قبل المعالجة: $_selectedBrandId');
        debugPrint('🔍 _loadBrands: عدد البراندات المحملة: ${brands.length}');
        for (var brand in brands) {
          debugPrint('🔍 براند: ${brand['name']} (${brand['id']})');
        }
      }

      setState(() {
        _brands = brands
            .map(
              (brand) => {
                'id': brand['id'],
                'name': brand['name'],
                'nameAr': brand['nameAr'] ?? brand['name'],
                'category': brand['category'] ?? 'all',
              },
            )
            .toList();

        // إصلاح brandId إذا كان غير موجود في القائمة
        if (_selectedBrandId != null && _selectedBrandId!.isNotEmpty) {
          // التحقق إذا كان البراند موجود
          bool brandExists = _brands.any((brand) => brand['name'] == _selectedBrandId);

          if (kDebugMode) {
            debugPrint('🔍 هل البراند موجود؟ $brandExists');
          }

          // إذا لم يوجد البراند، قم بإلغاء تحديده
          if (!brandExists) {
            if (kDebugMode) {
              debugPrint('⚠️ البراند "$_selectedBrandId" غير موجود، تم إلغاء التحديد');
            }
            _selectedBrandId = null;
          }
        }

        _isLoadingBrands = false;
      });

      if (kDebugMode) {
        debugPrint('🔍 البراند المحدد النهائي: $_selectedBrandId');
      }
    } catch (e) {
      if (kDebugMode) {
        debugPrint('❌ خطأ في تحميل البراندات: $e');
      }
      setState(() {
        _isLoadingBrands = false;
      });
    }
  }

  // دالة اختيار الصورة
  Future<void> _pickImage() async {
    try {
      if (kDebugMode) {
        debugPrint('🔄 بدء عملية اختيار الصورة...');
      }

      // للويب: استخدم image_picker بدلاً من file_picker
      if (kIsWeb) {
        await _pickImageWithImagePicker();
        return;
      }

      // للمنصات الأخرى: استخدم FilePicker العادي
      final result = await FilePicker.platform
          .pickFiles(
            type: FileType.custom,
            allowedExtensions: ['png', 'jpg', 'jpeg'],
            withData: true,
            allowMultiple: false,
            dialogTitle: 'اختر صورة المنتج',
          )
          .timeout(
            const Duration(seconds: 30),
            onTimeout: () {
              throw TimeoutException(
                'انتهت مهلة اختيار الملف',
                const Duration(seconds: 30),
              );
            },
          );

      if (result != null && result.files.isNotEmpty) {
        final file = result.files.first;
        if (file.bytes != null) {
          setState(() {
            _selectedImageBytes = file.bytes;
            _selectedImageName = file.name;
            _selectedImageType = file.extension?.toLowerCase() ?? 'jpeg';
          });

          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('تم اختيار الصورة: ${file.name}'),
                backgroundColor: AppColors.primaryColor,
              ),
            );
          }
        }
      }
    } catch (e) {
      if (kDebugMode) {
        debugPrint('❌ خطأ في اختيار الصورة: $e');
      }

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('فشل في اختيار الصورة: ${e.toString()}'),
            backgroundColor: AppColors.errorColor,
          ),
        );
      }
    }
  }

  Future<void> _pickImageWithImagePicker() async {
    try {
      if (kDebugMode) {
        debugPrint('🌐 استخدام ImagePicker لاختيار الصورة...');
      }

      final ImagePicker picker = ImagePicker();

      final XFile? image = await picker.pickImage(
        source: ImageSource.gallery,
        imageQuality: 100, // جودة كاملة
        // إزالة maxWidth و maxHeight للحفاظ على الدقة الأصلية
      );

      if (image != null) {
        final bytes = await image.readAsBytes();

        if (bytes.length > 20 * 1024 * 1024) {
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('حجم الصورة كبير جداً. الحد الأقصى 20MB'),
                backgroundColor: AppColors.errorColor,
              ),
            );
          }
          return;
        }

        String imageType = 'jpeg';
        if (image.name.toLowerCase().endsWith('.png')) {
          imageType = 'png';
        }

        setState(() {
          _selectedImageBytes = bytes;
          _selectedImageName = image.name;
          _selectedImageType = imageType;
        });

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('تم اختيار الصورة: ${image.name}'),
              backgroundColor: AppColors.primaryColor,
            ),
          );
        }
      }
    } catch (e) {
      if (kDebugMode) {
        debugPrint('❌ خطأ في ImagePicker: $e');
      }

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('فشل في اختيار الصورة: ${e.toString()}'),
            backgroundColor: AppColors.errorColor,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.scaffoldBackground,
      appBar: AppBar(
        title: const Text('تعديل المنتج'),
        backgroundColor: AppColors.primaryColor,
        foregroundColor: AppColors.white,
        elevation: 0,
        centerTitle: true,
        actions: [
          TextButton(
            onPressed: _isLoading ? null : _saveProduct,
            child: const Text(
              'حفظ',
              style: TextStyle(
                color: AppColors.white,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      ),
      body: Form(
        key: _formKey,
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // معلومات أساسية
              _buildSectionCard(
                title: 'المعلومات الأساسية',
                children: [
                  _buildTextField(
                    controller: _nameController,
                    label: 'اسم المنتج',
                    hint: 'أدخل اسم المنتج',
                    validator: (value) {
                      if (value == null || value.trim().isEmpty) {
                        return 'يرجى إدخال اسم المنتج';
                      }
                      return null;
                    },
                  ),
                  const SizedBox(height: 16),
                  _buildTextField(
                    controller: _descriptionController,
                    label: 'وصف المنتج',
                    hint: 'أدخل وصف تفصيلي للمنتج',
                    maxLines: 4,
                    validator: (value) {
                      if (value == null || value.trim().isEmpty) {
                        return 'يرجى إدخال وصف المنتج';
                      }
                      return null;
                    },
                  ),
                  const SizedBox(height: 16),
                  _buildTextField(
                    controller: _skuController,
                    label: 'رمز المنتج (SKU)',
                    hint: 'أدخل رمز المنتج الفريد',
                  ),
                ],
              ),

              const SizedBox(height: 20),

              // الفئة والبراند
              _buildSectionCard(
                title: 'الفئة والعلامة التجارية',
                children: [
                  // اختيار الفئة
                  _categories.isEmpty
                      ? const Center(child: CircularProgressIndicator())
                      : DropdownButtonFormField<String>(
                          value: _selectedCategoryId,
                          decoration: const InputDecoration(
                            labelText: 'الفئة',
                            border: OutlineInputBorder(),
                            focusedBorder: OutlineInputBorder(
                              borderSide: BorderSide(
                                color: AppColors.primaryColor,
                              ),
                            ),
                          ),
                          items: _categories.map((category) {
                            return DropdownMenuItem<String>(
                              value: category.id,
                              child: Text(category.name),
                            );
                          }).toList(),
                          onChanged: (value) {
                            setState(() {
                              _selectedCategoryId = value;
                            });
                            if (kDebugMode) {
                              debugPrint('🔄 تم اختيار الفئة: $value');
                            }
                          },
                          validator: (value) {
                            if (value == null || value.isEmpty) {
                              return 'يرجى اختيار الفئة';
                            }
                            return null;
                          },
                        ),
                  const SizedBox(height: 16),

                  // اختيار البراند
                  _isLoadingBrands
                      ? const Center(child: CircularProgressIndicator())
                      : DropdownButtonFormField<String>(
                          value: _selectedBrandId,
                          decoration: const InputDecoration(
                            labelText: 'العلامة التجارية',
                            border: OutlineInputBorder(),
                            focusedBorder: OutlineInputBorder(
                              borderSide: BorderSide(
                                color: AppColors.primaryColor,
                              ),
                            ),
                          ),
                          items: [
                            const DropdownMenuItem<String>(
                              value: null,
                              child: Text('اختر العلامة التجارية'),
                            ),
                            ..._brands.map((brand) {
                              return DropdownMenuItem<String>(
                                value: brand['name'],
                                child: Text(brand['nameAr'] ?? brand['name']),
                              );
                            }),
                          ],
                          onChanged: (value) {
                            setState(() {
                              _selectedBrandId = value;
                              if (value != null) {
                                _brandController.text = value;
                              }
                            });
                          },
                        ),
                ],
              ),

              const SizedBox(height: 20),

              // السعر والمخزون
              _buildSectionCard(
                title: 'السعر والمخزون',
                children: [
                  Row(
                    children: [
                      Expanded(
                        child: _buildTextField(
                          controller: _priceController,
                          label: 'السعر (IQD)',
                          hint: '0',
                          keyboardType: TextInputType.number,
                          validator: (value) {
                            if (value == null || value.trim().isEmpty) {
                              return 'يرجى إدخال السعر';
                            }
                            if (double.tryParse(value) == null) {
                              return 'يرجى إدخال رقم صحيح';
                            }
                            return null;
                          },
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: _buildTextField(
                          controller: _stockController,
                          label: 'الكمية المتوفرة',
                          hint: '0',
                          keyboardType: TextInputType.number,
                          validator: (value) {
                            if (value == null || value.trim().isEmpty) {
                              return 'يرجى إدخال الكمية';
                            }
                            if (int.tryParse(value) == null) {
                              return 'يرجى إدخال رقم صحيح';
                            }
                            return null;
                          },
                        ),
                      ),
                    ],
                  ),
                ],
              ),

              const SizedBox(height: 20),

              // صورة المنتج
              _buildSectionCard(
                title: 'صورة المنتج',
                children: [
                  // عرض الصورة الحالية
                  if (_existingMainImage.isNotEmpty ||
                      _selectedImageBytes != null)
                    Container(
                      height: 200,
                      width: double.infinity,
                      margin: const EdgeInsets.only(bottom: 16),
                      decoration: BoxDecoration(
                        border: Border.all(
                          color: AppColors.grey.withValues(alpha: 0.3),
                        ),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: ClipRRect(
                        borderRadius: BorderRadius.circular(8),
                        child: _selectedImageBytes != null
                            ? Image.memory(
                                _selectedImageBytes!,
                                fit: BoxFit.cover,
                              )
                            : Image.network(
                                _existingMainImage,
                                fit: BoxFit.cover,
                                errorBuilder: (context, error, stackTrace) {
                                  return Container(
                                    color: AppColors.lightGrey,
                                    child: const Center(
                                      child: Icon(
                                        Icons.broken_image,
                                        size: 50,
                                        color: AppColors.grey,
                                      ),
                                    ),
                                  );
                                },
                              ),
                      ),
                    ),

                  // زر اختيار صورة جديدة
                  ElevatedButton.icon(
                    onPressed: _pickImage,
                    icon: const Icon(Icons.upload_file),
                    label: Text(
                      _selectedImageBytes != null
                          ? 'تغيير الصورة'
                          : 'اختر صورة جديدة',
                    ),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppColors.primaryColor,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(
                        horizontal: 20,
                        vertical: 12,
                      ),
                    ),
                  ),

                  if (_selectedImageName != null)
                    Padding(
                      padding: const EdgeInsets.only(top: 8),
                      child: Text(
                        'الصورة المختارة: $_selectedImageName',
                        style: const TextStyle(
                          fontSize: 12,
                          color: AppColors.grey,
                        ),
                      ),
                    ),
                ],
              ),

              const SizedBox(height: 20),

              // إعدادات إضافية
              _buildSectionCard(
                title: 'إعدادات إضافية',
                children: [
                  CheckboxListTile(
                    title: const Text('منتج مميز'),
                    subtitle: const Text('سيظهر في قسم المنتجات المميزة'),
                    value: _isFeatured,
                    onChanged: (value) {
                      setState(() {
                        _isFeatured = value ?? false;
                      });
                    },
                    activeColor: AppColors.primaryColor,
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSectionCard({
    required String title,
    required List<Widget> children,
  }) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: AppColors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: const TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: AppColors.primaryColor,
            ),
          ),
          const SizedBox(height: 16),
          ...children,
        ],
      ),
    );
  }

  Widget _buildTextField({
    required TextEditingController controller,
    required String label,
    String? hint,
    int maxLines = 1,
    TextInputType? keyboardType,
    String? Function(String?)? validator,
  }) {
    return TextFormField(
      controller: controller,
      decoration: InputDecoration(
        labelText: label,
        hintText: hint,
        border: const OutlineInputBorder(),
        focusedBorder: const OutlineInputBorder(
          borderSide: BorderSide(color: AppColors.primaryColor),
        ),
      ),
      maxLines: maxLines,
      keyboardType: keyboardType,
      validator: validator,
    );
  }

  Future<void> _saveProduct() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      // التحقق من وجود الفئة المحددة
      if (_selectedCategoryId == null || _categories.isEmpty) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('يرجى اختيار الفئة أولاً'),
              backgroundColor: AppColors.errorColor,
            ),
          );
        }
        return;
      }

      // البحث عن اسم الفئة مع معالجة آمنة
      category_model.Category? selectedCategory;

      try {
        selectedCategory = _categories.firstWhere(
          (cat) => cat.id == _selectedCategoryId,
        );
      } catch (e) {
        if (kDebugMode) {
          debugPrint('❌ لم يتم العثور على الفئة: $_selectedCategoryId');
          debugPrint(
            '📋 الفئات المتاحة: ${_categories.map((c) => '${c.name} (${c.id})').join(', ')}',
          );
        }

        // استخدام أول فئة متاحة كبديل
        if (_categories.isNotEmpty) {
          selectedCategory = _categories.first;
          _selectedCategoryId = selectedCategory.id;
        } else {
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('لا توجد فئات متاحة. يرجى إضافة فئة أولاً'),
                backgroundColor: AppColors.errorColor,
              ),
            );
          }
          return;
        }
      }

      if (kDebugMode) {
        debugPrint(
          '🔍 الفئة المحددة: ${selectedCategory.name} (${selectedCategory.id})',
        );
        debugPrint('🏷️ البراند المحدد: $_selectedBrandId');
      }

      // تحديد الصور النهائية
      String finalMainImage = _existingMainImage;
      List<String> finalAdditionalImages = List.from(_existingAdditionalImages);

      // إذا تم اختيار صورة جديدة، قم برفعها إلى Firebase Storage
      if (_selectedImageBytes != null) {
        try {
          if (kDebugMode) {
            debugPrint('🔄 رفع الصورة الجديدة إلى Firebase Storage...');
            debugPrint('📏 حجم الصورة: ${_selectedImageBytes!.length} بايت');
          }

          // رفع الصورة إلى Firebase Storage بجودتها الأصلية
          final uploadedUrl = await FirebaseStorageService.uploadProductImage(
            widget.product.id,
            _selectedImageBytes!,
            _selectedImageName ?? 'updated_image.jpg',
          );

          if (uploadedUrl != null) {
            finalMainImage = uploadedUrl;
            finalAdditionalImages = [uploadedUrl];
            if (kDebugMode) {
              debugPrint('✅ تم رفع الصورة الجديدة إلى Firebase Storage بنجاح');
              debugPrint('🔗 رابط الصورة: $uploadedUrl');
            }
          } else {
            throw Exception('فشل في رفع الصورة إلى Firebase Storage');
          }
        } catch (e) {
          if (kDebugMode) {
            debugPrint('❌ خطأ في رفع الصورة: $e');
          }
          // في حالة فشل رفع الصورة، استخدم الصورة الموجودة
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('فشل في رفع الصورة: $e'),
                backgroundColor: AppColors.errorColor,
              ),
            );
          }
        }
      }

      // إنشاء منتج محدث
      final updatedProduct = widget.product.copyWith(
        name: _nameController.text.trim(),
        description: _descriptionController.text.trim(),
        price: double.parse(_priceController.text),
        originalPrice: double.parse(_priceController.text),
        image: finalMainImage,
        images: finalAdditionalImages,
        categoryId: _selectedCategoryId!,
        categoryName: selectedCategory.name,
        inStock: int.parse(_stockController.text) > 0,
        stockQuantity: int.parse(_stockController.text),
        brand: _selectedBrandId,
        stock: int.parse(_stockController.text),
        updatedAt: DateTime.now(),
        isFeatured: _isFeatured,
        tags: _skuController.text.trim().isEmpty
            ? null
            : _skuController.text.trim(),
      );

      // محاكاة حفظ المنتج
      await Future.delayed(const Duration(seconds: 1));

      // حفظ المنتج في Firestore أولاً
      try {
        if (kDebugMode) {
          debugPrint('🔄 بدء تحديث المنتج في Firestore...');
          debugPrint('📝 اسم المنتج: ${updatedProduct.name}');
          debugPrint('📂 الفئة: ${updatedProduct.categoryName}');
          debugPrint('🏷️ البراند: ${updatedProduct.brand}');
        }

        await FirestoreDataService.updateProduct(updatedProduct);

        if (kDebugMode) {
          debugPrint('✅ تم تحديث المنتج في Firestore: ${updatedProduct.name}');
        }
      } catch (e) {
        if (kDebugMode) {
          debugPrint('❌ خطأ في تحديث المنتج في Firestore: $e');
        }

        // في حالة فشل Firestore، احفظ محلياً
        try {
          await ApiService.updateProduct(updatedProduct);
          if (kDebugMode) {
            debugPrint('✅ تم تحديث المنتج محلياً: ${updatedProduct.name}');
          }
        } catch (localError) {
          if (kDebugMode) {
            debugPrint('❌ خطأ في الحفظ المحلي أيضاً: $localError');
          }
          rethrow; // إعادة رمي الخطأ للمعالجة في catch الخارجي
        }
      }

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم تحديث المنتج بنجاح'),
            backgroundColor: AppColors.successColor,
          ),
        );
        Navigator.pop(context, true); // إرجاع true للإشارة إلى نجاح التحديث
      }
    } catch (e) {
      if (kDebugMode) print('خطأ في تحديث المنتج: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('حدث خطأ أثناء تحديث المنتج'),
            backgroundColor: AppColors.errorColor,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  /// تحديث معرف الفئة في Firebase للمنتج الحالي
  Future<void> _updateProductCategoryInFirebase(String correctCategoryId) async {
    try {
      if (kDebugMode) {
        debugPrint('🔄 تحديث categoryId في Firebase من "${widget.product.categoryId}" إلى "$correctCategoryId"');
      }

      await FirebaseFirestore.instance
          .collection('products')
          .doc(widget.product.id)
          .update({'categoryId': correctCategoryId});

      if (kDebugMode) {
        debugPrint('✅ تم تحديث categoryId في Firebase بنجاح');
      }
    } catch (e) {
      if (kDebugMode) {
        debugPrint('❌ خطأ في تحديث categoryId في Firebase: $e');
      }
    }
  }
}
