import 'package:flutter/foundation.dart';
import '../models/product.dart';
import '../models/review.dart';
import 'firebase_mobile_service.dart';

/// خدمة Firebase موحدة تختار الخدمة المناسبة حسب المنصة
class FirebasePlatformService {
  static bool _isInitialized = false;

  /// تهيئة الخدمة
  static Future<bool> initialize() async {
    try {
      if (_isInitialized) {
        return true;
      }

      // للموبايل: استخدم Firebase Mobile Service فقط
      final success = await FirebaseMobileService.initialize();
      _isInitialized = success;
      return success;
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في تهيئة Firebase Platform Service: $e');
      }
      return false;
    }
  }

  /// جلب المنتجات
  static Future<List<Product>> getProducts() async {
    try {
      if (!_isInitialized) {
        await initialize();
      }

      // للموبايل: استخدم Firebase Mobile Service فقط
      return await FirebaseMobileService.getProducts();
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في جلب المنتجات: $e');
      }
      return [];
    }
  }

  /// إضافة منتج
  static Future<bool> addProduct(Product product) async {
    try {
      if (!_isInitialized) {
        await initialize();
      }

      // للموبايل: استخدم Firebase Mobile Service فقط
      return await FirebaseMobileService.addProduct(product);
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في إضافة المنتج: $e');
      }
      return false;
    }
  }

  /// تعديل منتج
  static Future<bool> updateProduct(Product product) async {
    try {
      if (!_isInitialized) {
        await initialize();
      }

      // للموبايل: استخدم Firebase Mobile Service فقط
      return await FirebaseMobileService.updateProduct(product);
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في تعديل المنتج: $e');
      }
      return false;
    }
  }

  /// حذف منتج
  static Future<bool> deleteProduct(String productId) async {
    try {
      if (!_isInitialized) {
        await initialize();
      }

      // للموبايل: استخدم Firebase Mobile Service فقط
      return await FirebaseMobileService.deleteProduct(productId);
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في حذف المنتج: $e');
      }
      return false;
    }
  }

  /// جلب الفئات
  static Future<List<Map<String, dynamic>>> getCategories() async {
    try {
      if (!_isInitialized) {
        await initialize();
      }

      // للموبايل: استخدم Firebase Mobile Service فقط
      return await FirebaseMobileService.getCategories();
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في جلب الفئات: $e');
      }
      return [];
    }
  }

  /// إضافة فئة
  static Future<bool> addCategory(Map<String, dynamic> categoryData) async {
    try {
      if (!_isInitialized) {
        await initialize();
      }

      // للموبايل: استخدم Firebase Mobile Service فقط
      return await FirebaseMobileService.addCategory(categoryData);
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في إضافة الفئة: $e');
      }
      return false;
    }
  }

  /// تعديل فئة
  static Future<bool> updateCategory(Map<String, dynamic> categoryData) async {
    try {
      if (!_isInitialized) {
        await initialize();
      }

      // للموبايل: استخدم Firebase Mobile Service فقط
      return await FirebaseMobileService.updateCategory(categoryData);
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في تعديل الفئة: $e');
      }
      return false;
    }
  }

  /// حذف فئة
  static Future<bool> deleteCategory(String categoryId) async {
    try {
      if (!_isInitialized) {
        await initialize();
      }

      // للموبايل: استخدم Firebase Mobile Service فقط
      return await FirebaseMobileService.deleteCategory(categoryId);
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في حذف الفئة: $e');
      }
      return false;
    }
  }

  /// جلب المراجعات
  static Future<List<Review>> getProductReviews(String productId) async {
    try {
      if (!_isInitialized) {
        await initialize();
      }

      // للموبايل: استخدم Firebase Mobile Service فقط
      return await FirebaseMobileService.getProductReviews(productId);
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في جلب المراجعات: $e');
      }
      return [];
    }
  }

  /// إضافة مراجعة
  static Future<bool> addReview(Review review) async {
    try {
      if (!_isInitialized) {
        await initialize();
      }

      // للموبايل: استخدم Firebase Mobile Service فقط
      return await FirebaseMobileService.addReview(review);
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في إضافة المراجعة: $e');
      }
      return false;
    }
  }

  /// جلب الطلبات
  static Future<List<Map<String, dynamic>>> getOrders() async {
    try {
      if (!_isInitialized) {
        await initialize();
      }

      // للموبايل: استخدم Firebase Mobile Service فقط
      return await FirebaseMobileService.getOrders();
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في جلب الطلبات: $e');
      }
      return [];
    }
  }

  /// إضافة طلب
  static Future<bool> addOrder(Map<String, dynamic> orderData) async {
    try {
      if (!_isInitialized) {
        await initialize();
      }

      // للموبايل: استخدم Firebase Mobile Service فقط
      return await FirebaseMobileService.addOrder(orderData);
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في إضافة الطلب: $e');
      }
      return false;
    }
  }

  /// جلب العلامات التجارية
  static Future<List<Map<String, dynamic>>> getBrands() async {
    try {
      if (!_isInitialized) {
        await initialize();
      }

      // للموبايل: استخدم Firebase Mobile Service فقط
      return await FirebaseMobileService.getBrands();
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في جلب العلامات التجارية: $e');
      }
      return [];
    }
  }

  /// إضافة علامة تجارية
  static Future<bool> addBrand(Map<String, dynamic> brandData) async {
    try {
      if (!_isInitialized) {
        await initialize();
      }

      // للموبايل: استخدم Firebase Mobile Service فقط
      return await FirebaseMobileService.addBrand(brandData);
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في إضافة العلامة التجارية: $e');
      }
      return false;
    }
  }



  /// تعديل علامة تجارية
  static Future<bool> updateBrand(Map<String, dynamic> brandData) async {
    try {
      if (!_isInitialized) {
        await initialize();
      }

      // للموبايل: استخدم Firebase Mobile Service فقط
      return await FirebaseMobileService.updateBrand(brandData);
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في تعديل العلامة التجارية: $e');
      }
      return false;
    }
  }

  /// حذف علامة تجارية
  static Future<bool> deleteBrand(String brandId) async {
    try {
      if (!_isInitialized) {
        await initialize();
      }

      // للموبايل: استخدم Firebase Mobile Service فقط
      return await FirebaseMobileService.deleteBrand(brandId);
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في حذف العلامة التجارية: $e');
      }
      return false;
    }
  }

  /// جلب المستخدمين
  static Future<List<Map<String, dynamic>>> getUsers() async {
    try {
      if (!_isInitialized) {
        await initialize();
      }

      // للموبايل: استخدم Firebase Mobile Service فقط
      return await FirebaseMobileService.getUsers();
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في جلب المستخدمين: $e');
      }
      return [];
    }
  }

  /// تحديث طلب
  static Future<bool> updateOrder(
    String orderId,
    Map<String, dynamic> orderData,
  ) async {
    try {
      if (!_isInitialized) {
        await initialize();
      }

      // للموبايل: استخدم Firebase Mobile Service فقط
      return await FirebaseMobileService.updateOrder(orderId, orderData);
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في تحديث الطلب: $e');
      }
      return false;
    }
  }

  /// تحديث حالة الطلب فقط
  static Future<bool> updateOrderStatus(String orderId, String status) async {
    try {
      if (!_isInitialized) {
        await initialize();
      }

      if (kDebugMode) {
        print('🔄 تحديث حالة الطلب: $orderId إلى $status');
      }

      // للموبايل: استخدم Firebase Mobile Service فقط
      final orderData = {
        'status': status,
        'updatedAt': DateTime.now().toIso8601String(),
      };

      final success = await FirebaseMobileService.updateOrder(orderId, orderData);

      if (success && kDebugMode) {
        print('✅ تم تحديث حالة الطلب بنجاح في Firebase');
      }

      return success;
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في تحديث حالة الطلب: $e');
      }
      return false;
    }
  }
}
