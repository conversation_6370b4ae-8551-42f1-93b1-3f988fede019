import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import '../../app_properties.dart';
import '../../services/app_state.dart';
import '../../services/notification_settings_service.dart';

class NotificationSettingsPage extends StatefulWidget {
  const NotificationSettingsPage({super.key});

  @override
  State<NotificationSettingsPage> createState() => _NotificationSettingsPageState();
}

class _NotificationSettingsPageState extends State<NotificationSettingsPage> {
  final AppState _appState = AppState();

  // إعدادات الإشعارات
  bool _orderNotifications = true;
  bool _promotionNotifications = true;
  bool _newsNotifications = false;
  bool _reminderNotifications = true;
  bool _soundEnabled = true;
  bool _vibrationEnabled = true;

  String _selectedSound = 'افتراضي';
  TimeOfDay _quietHoursStart = const TimeOfDay(hour: 22, minute: 0);
  TimeOfDay _quietHoursEnd = const TimeOfDay(hour: 8, minute: 0);
  bool _quietHoursEnabled = false;

  bool _isLoading = true;
  bool _isSaving = false;

  final List<String> _soundOptions = [
    'افتراضي',
    'نغمة 1',
    'نغمة 2',
    'نغمة 3',
    'بدون صوت',
  ];

  @override
  void initState() {
    super.initState();
    _loadSettings();
  }

  /// تحميل الإعدادات المحفوظة
  Future<void> _loadSettings() async {
    try {
      if (kDebugMode) {
        print('📱 تحميل إعدادات الإشعارات...');
      }

      final settings = await NotificationSettingsService.getAllSettings();
      final quietStart = settings['quietHoursStart'] as Map<String, int>;
      final quietEnd = settings['quietHoursEnd'] as Map<String, int>;

      if (mounted) {
        setState(() {
          _orderNotifications = settings['orderNotifications'] as bool;
          _promotionNotifications = settings['promotionNotifications'] as bool;
          _newsNotifications = settings['newsNotifications'] as bool;
          _reminderNotifications = settings['reminderNotifications'] as bool;
          _soundEnabled = settings['soundEnabled'] as bool;
          _vibrationEnabled = settings['vibrationEnabled'] as bool;
          _selectedSound = settings['selectedSound'] as String;
          _quietHoursEnabled = settings['quietHoursEnabled'] as bool;
          _quietHoursStart = TimeOfDay(
            hour: quietStart['hour']!,
            minute: quietStart['minute']!,
          );
          _quietHoursEnd = TimeOfDay(
            hour: quietEnd['hour']!,
            minute: quietEnd['minute']!,
          );
          _isLoading = false;
        });
      }

      if (kDebugMode) {
        print('✅ تم تحميل إعدادات الإشعارات بنجاح');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في تحميل إعدادات الإشعارات: $e');
      }

      if (mounted) {
        setState(() {
          _isLoading = false;
        });

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في تحميل الإعدادات: $e'),
            backgroundColor: AppColors.errorColor,
          ),
        );
      }
    }
  }

  /// حفظ إعداد معين
  Future<void> _saveSetting(String settingName, dynamic value) async {
    if (_isSaving) return;

    setState(() {
      _isSaving = true;
    });

    try {
      switch (settingName) {
        case 'orderNotifications':
          await NotificationSettingsService.setOrderNotifications(value as bool);
          break;
        case 'promotionNotifications':
          await NotificationSettingsService.setPromotionNotifications(value as bool);
          break;
        case 'newsNotifications':
          await NotificationSettingsService.setNewsNotifications(value as bool);
          break;
        case 'reminderNotifications':
          await NotificationSettingsService.setReminderNotifications(value as bool);
          break;
        case 'soundEnabled':
          await NotificationSettingsService.setSoundEnabled(value as bool);
          break;
        case 'vibrationEnabled':
          await NotificationSettingsService.setVibrationEnabled(value as bool);
          break;
        case 'selectedSound':
          await NotificationSettingsService.setSelectedSound(value as String);
          break;
        case 'quietHoursEnabled':
          await NotificationSettingsService.setQuietHoursEnabled(value as bool);
          break;
        case 'quietHoursStart':
          final time = value as TimeOfDay;
          await NotificationSettingsService.setQuietHoursStart(time.hour, time.minute);
          break;
        case 'quietHoursEnd':
          final time = value as TimeOfDay;
          await NotificationSettingsService.setQuietHoursEnd(time.hour, time.minute);
          break;
      }

      if (kDebugMode) {
        print('✅ تم حفظ إعداد $settingName: $value');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في حفظ إعداد $settingName: $e');
      }

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في حفظ الإعداد: $e'),
            backgroundColor: AppColors.errorColor,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isSaving = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return Scaffold(
        backgroundColor: AppColors.backgroundColor,
        appBar: AppBar(
          backgroundColor: AppColors.primaryColor,
          foregroundColor: AppColors.white,
          title: const Row(
            children: [
              Icon(Icons.notifications, size: 24),
              SizedBox(width: 8),
              Text('إعدادات الإشعارات'),
            ],
          ),
          elevation: 0,
        ),
        body: const Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              CircularProgressIndicator(),
              SizedBox(height: 16),
              Text('جاري تحميل الإعدادات...'),
            ],
          ),
        ),
      );
    }
    return Scaffold(
      backgroundColor: AppColors.backgroundColor,
      appBar: AppBar(
        backgroundColor: AppColors.primaryColor,
        foregroundColor: AppColors.white,
        title: const Row(
          children: [
            Icon(Icons.notifications, size: 24),
            SizedBox(width: 8),
            Text('إعدادات الإشعارات'),
          ],
        ),
        elevation: 0,
      ),
      body: SingleChildScrollView(
        child: Column(
          children: [
            // أنواع الإشعارات
            _buildNotificationTypesSection(),
            
            // إعدادات الصوت والاهتزاز
            _buildSoundVibrationSection(),
            
            // الساعات الهادئة
            _buildQuietHoursSection(),
            
            // إعدادات متقدمة
            _buildAdvancedSection(),
            
            const SizedBox(height: 20),
          ],
        ),
      ),
    );
  }

  Widget _buildNotificationTypesSection() {
    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: AppColors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: AppColors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: AppColors.primaryColor.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Icon(
                  Icons.notifications_active,
                  color: AppColors.primaryColor,
                  size: 20,
                ),
              ),
              const SizedBox(width: 12),
              const Text(
                'أنواع الإشعارات',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 20),
          
          _buildNotificationToggle(
            title: 'إشعارات الطلبات',
            subtitle: 'تحديثات حالة الطلب والشحن',
            value: _orderNotifications,
            onChanged: (value) {
              setState(() {
                _orderNotifications = value;
              });
              _saveSetting('orderNotifications', value);
            },
            icon: Icons.shopping_bag_outlined,
            color: AppColors.primaryColor,
          ),
          
          _buildNotificationToggle(
            title: 'العروض والخصومات',
            subtitle: 'إشعارات العروض الخاصة والتخفيضات',
            value: _promotionNotifications,
            onChanged: (value) {
              setState(() {
                _promotionNotifications = value;
              });
              _saveSetting('promotionNotifications', value);
            },
            icon: Icons.local_offer,
            color: AppColors.warningColor,
          ),
          
          _buildNotificationToggle(
            title: 'الأخبار والتحديثات',
            subtitle: 'منتجات جديدة وأخبار الشركة',
            value: _newsNotifications,
            onChanged: (value) {
              setState(() {
                _newsNotifications = value;
              });
              _saveSetting('newsNotifications', value);
            },
            icon: Icons.newspaper,
            color: AppColors.accentColor,
          ),
          
          _buildNotificationToggle(
            title: 'التذكيرات',
            subtitle: 'تذكير بالعربة المتروكة والمواعيد',
            value: _reminderNotifications,
            onChanged: (value) {
              setState(() {
                _reminderNotifications = value;
              });
              _saveSetting('reminderNotifications', value);
            },
            icon: Icons.schedule,
            color: AppColors.success,
          ),
        ],
      ),
    );
  }

  Widget _buildNotificationToggle({
    required String title,
    required String subtitle,
    required bool value,
    required ValueChanged<bool> onChanged,
    required IconData icon,
    required Color color,
  }) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: color.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              icon,
              color: color,
              size: 20,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const SizedBox(height: 2),
                Text(
                  subtitle,
                  style: TextStyle(
                    fontSize: 12,
                    color: AppColors.textColor.withValues(alpha: 0.6),
                  ),
                ),
              ],
            ),
          ),
          Switch(
            value: value,
            onChanged: onChanged,
            activeColor: AppColors.primaryColor,
          ),
        ],
      ),
    );
  }

  Widget _buildSoundVibrationSection() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: AppColors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: AppColors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: AppColors.accentColor.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Icon(
                  Icons.volume_up,
                  color: AppColors.accentColor,
                  size: 20,
                ),
              ),
              const SizedBox(width: 12),
              const Text(
                'الصوت والاهتزاز',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 20),
          
          // تفعيل الصوت
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'تفعيل الصوت',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  Text(
                    'تشغيل صوت عند وصول الإشعارات',
                    style: TextStyle(
                      fontSize: 12,
                      color: AppColors.textColor,
                    ),
                  ),
                ],
              ),
              Switch(
                value: _soundEnabled,
                onChanged: (value) {
                  setState(() {
                    _soundEnabled = value;
                  });
                  _saveSetting('soundEnabled', value);
                },
                activeColor: AppColors.primaryColor,
              ),
            ],
          ),
          
          const SizedBox(height: 16),
          
          // اختيار نغمة الإشعار
          if (_soundEnabled) ...[
            const Text(
              'نغمة الإشعار',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 8),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 12),
              decoration: BoxDecoration(
                border: Border.all(color: AppColors.lightGrey),
                borderRadius: BorderRadius.circular(8),
              ),
              child: DropdownButton<String>(
                value: _selectedSound,
                isExpanded: true,
                underline: const SizedBox(),
                items: _soundOptions.map((sound) {
                  return DropdownMenuItem(
                    value: sound,
                    child: Text(sound),
                  );
                }).toList(),
                onChanged: (value) {
                  if (value != null) {
                    setState(() {
                      _selectedSound = value;
                    });
                    _saveSetting('selectedSound', value);
                  }
                },
              ),
            ),
            const SizedBox(height: 16),
          ],
          
          // تفعيل الاهتزاز
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'تفعيل الاهتزاز',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  Text(
                    'اهتزاز الجهاز عند وصول الإشعارات',
                    style: TextStyle(
                      fontSize: 12,
                      color: AppColors.textColor,
                    ),
                  ),
                ],
              ),
              Switch(
                value: _vibrationEnabled,
                onChanged: (value) {
                  setState(() {
                    _vibrationEnabled = value;
                  });
                  _saveSetting('vibrationEnabled', value);
                },
                activeColor: AppColors.primaryColor,
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildQuietHoursSection() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: AppColors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: AppColors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: AppColors.success.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Icon(
                  Icons.bedtime,
                  color: AppColors.success,
                  size: 20,
                ),
              ),
              const SizedBox(width: 12),
              const Text(
                'الساعات الهادئة',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 20),
          
          // تفعيل الساعات الهادئة
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'تفعيل الساعات الهادئة',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  Text(
                    'إيقاف الإشعارات في أوقات محددة',
                    style: TextStyle(
                      fontSize: 12,
                      color: AppColors.textColor,
                    ),
                  ),
                ],
              ),
              Switch(
                value: _quietHoursEnabled,
                onChanged: (value) {
                  setState(() {
                    _quietHoursEnabled = value;
                  });
                  _saveSetting('quietHoursEnabled', value);
                },
                activeColor: AppColors.primaryColor,
              ),
            ],
          ),
          
          if (_quietHoursEnabled) ...[
            const SizedBox(height: 16),
            
            // وقت البداية
            _buildTimeSelector(
              title: 'بداية الساعات الهادئة',
              time: _quietHoursStart,
              onTimeChanged: (time) {
                setState(() {
                  _quietHoursStart = time;
                });
                _saveSetting('quietHoursStart', time);
              },
            ),
            
            const SizedBox(height: 12),
            
            // وقت النهاية
            _buildTimeSelector(
              title: 'نهاية الساعات الهادئة',
              time: _quietHoursEnd,
              onTimeChanged: (time) {
                setState(() {
                  _quietHoursEnd = time;
                });
                _saveSetting('quietHoursEnd', time);
              },
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildTimeSelector({
    required String title,
    required TimeOfDay time,
    required ValueChanged<TimeOfDay> onTimeChanged,
  }) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          title,
          style: const TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w500,
          ),
        ),
        InkWell(
          onTap: () async {
            final selectedTime = await showTimePicker(
              context: context,
              initialTime: time,
            );
            if (selectedTime != null) {
              onTimeChanged(selectedTime);
            }
          },
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
            decoration: BoxDecoration(
              border: Border.all(color: AppColors.lightGrey),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Text(
              time.format(context),
              style: const TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildAdvancedSection() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: AppColors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: AppColors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: AppColors.warningColor.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Icon(
                  Icons.settings,
                  color: AppColors.warningColor,
                  size: 20,
                ),
              ),
              const SizedBox(width: 12),
              const Text(
                'إعدادات متقدمة',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 20),
          
          // اختبار الإشعارات
          ListTile(
            leading: const Icon(Icons.notifications_active),
            title: const Text('اختبار الإشعارات'),
            subtitle: const Text('إرسال إشعار تجريبي'),
            trailing: const Icon(Icons.arrow_forward_ios, size: 16),
            onTap: _sendTestNotification,
          ),
          
          const Divider(),
          
          // إعدادات النظام
          ListTile(
            leading: const Icon(Icons.settings_applications),
            title: const Text('إعدادات النظام'),
            subtitle: const Text('فتح إعدادات الإشعارات في النظام'),
            trailing: const Icon(Icons.arrow_forward_ios, size: 16),
            onTap: _openSystemSettings,
          ),
        ],
      ),
    );
  }

  Future<void> _sendTestNotification() async {
    try {
      // التحقق من إمكانية إرسال الإشعارات
      final canSend = await NotificationSettingsService.canSendNotification('test');

      if (!canSend) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Row(
                children: [
                  Icon(Icons.info, color: AppColors.white),
                  SizedBox(width: 8),
                  Text('الإشعارات معطلة حالياً (الساعات الهادئة)'),
                ],
              ),
              backgroundColor: AppColors.warningColor,
              behavior: SnackBarBehavior.floating,
            ),
          );
        }
        return;
      }

      // محاكاة إرسال إشعار تجريبي
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Row(
              children: [
                Icon(Icons.check, color: AppColors.white),
                SizedBox(width: 8),
                Text('تم إرسال إشعار تجريبي بنجاح!'),
              ],
            ),
            backgroundColor: AppColors.success,
            behavior: SnackBarBehavior.floating,
          ),
        );
      }

      if (kDebugMode) {
        print('📱 تم إرسال إشعار تجريبي');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في إرسال الإشعار التجريبي: $e');
      }

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                const Icon(Icons.error, color: AppColors.white),
                const SizedBox(width: 8),
                Text('فشل في إرسال الإشعار: $e'),
              ],
            ),
            backgroundColor: AppColors.errorColor,
            behavior: SnackBarBehavior.floating,
          ),
        );
      }
    }
  }

  void _openSystemSettings() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('فتح إعدادات النظام...'),
        behavior: SnackBarBehavior.floating,
      ),
    );
  }
}
