import 'package:flutter_test/flutter_test.dart';
import 'package:visionlens_app/services/encryption_service.dart';
import 'package:visionlens_app/services/jwt_service.dart';
import 'package:visionlens_app/services/api_security_service.dart';
import 'package:visionlens_app/services/secure_auth_service.dart';
import 'package:visionlens_app/services/secure_storage_service.dart';
import 'package:visionlens_app/models/user_simple.dart';

void main() {
  group('🛡️ اختبارات النظام الأمني', () {
    setUpAll(() async {
      // تهيئة خدمات التشفير للاختبار
      await EncryptionService.initialize();
    });

    group('🔐 اختبارات التشفير', () {
      test('تشفير وفك تشفير كلمة المرور', () {
        const password = 'TestPassword123!';
        
        // تشفير كلمة المرور
        final hashedPassword = EncryptionService.hashPassword(password);
        
        // التحقق من أن كلمة المرور مشفرة
        expect(hashedPassword, isNot(equals(password)));
        expect(hashedPassword.length, greaterThan(50));
        
        // التحقق من صحة كلمة المرور
        final isValid = EncryptionService.verifyPassword(password, hashedPassword);
        expect(isValid, isTrue);
        
        // التحقق من كلمة مرور خاطئة
        final isInvalid = EncryptionService.verifyPassword('WrongPassword', hashedPassword);
        expect(isInvalid, isFalse);
      });

      test('تشفير وفك تشفير البيانات الحساسة', () {
        const testDataString = '{"phone":"+1234567890","address":"123 Test Street"}';

        // تشفير البيانات
        final encryptedData = EncryptionService.encryptSensitiveData(testDataString);

        // التحقق من أن البيانات مشفرة
        expect(encryptedData, isNot(equals(testDataString)));
        expect(encryptedData, contains(':'));

        // فك تشفير البيانات
        final decryptedData = EncryptionService.decryptSensitiveData(encryptedData);

        // التحقق من صحة البيانات المفكوكة
        expect(decryptedData, equals(testDataString));
      });

      test('فحص قوة كلمة المرور', () {
        // كلمات مرور قوية
        expect(EncryptionService.isPasswordStrong('StrongPass123!'), isTrue);
        expect(EncryptionService.isPasswordStrong('MySecure@Pass2024'), isTrue);
        
        // كلمات مرور ضعيفة
        expect(EncryptionService.isPasswordStrong('weak'), isFalse);
        expect(EncryptionService.isPasswordStrong('12345678'), isFalse);
        expect(EncryptionService.isPasswordStrong('password'), isFalse);
        expect(EncryptionService.isPasswordStrong('PASSWORD'), isFalse);
      });
    });

    group('🎫 اختبارات JWT', () {
      late User testUser;

      setUp(() {
        testUser = User(
          id: 'test_user_123',
          email: '<EMAIL>',
          firstName: 'Test',
          lastName: 'User',
          isEmailVerified: true,
          role: UserRole.customer,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );
      });

      test('توليد والتحقق من Access Token', () {
        // توليد التوكن
        final accessToken = JWTService.generateAccessToken(testUser);
        
        // التحقق من وجود التوكن
        expect(accessToken, isNotEmpty);
        expect(accessToken.split('.').length, equals(3)); // JWT format
        
        // التحقق من صحة التوكن
        final isValid = JWTService.verifyToken(accessToken);
        expect(isValid, isTrue);
        
        // استخراج معلومات المستخدم
        final userInfo = JWTService.getUserFromToken(accessToken);
        expect(userInfo?['email'], equals(testUser.email));
        expect(userInfo?['userId'], equals(testUser.id));
      });

      test('توليد والتحقق من Refresh Token', () {
        // توليد التوكن
        final refreshToken = JWTService.generateRefreshToken(testUser);
        
        // التحقق من وجود التوكن
        expect(refreshToken, isNotEmpty);
        expect(refreshToken.split('.').length, equals(3));
        
        // التحقق من صحة التوكن
        final isValid = JWTService.verifyToken(refreshToken);
        expect(isValid, isTrue);
        
        // التحقق من نوع التوكن
        final tokenInfo = JWTService.getTokenInfo(refreshToken);
        expect(tokenInfo?['tokenType'], equals('refresh'));
      });

      test('انتهاء صلاحية التوكن', () async {
        // توليد توكن عادي
        final token = JWTService.generateAccessToken(testUser);

        // التحقق من صحة التوكن
        expect(JWTService.verifyToken(token), isTrue);

        // التحقق من معلومات التوكن
        final tokenInfo = JWTService.getTokenInfo(token);
        expect(tokenInfo, isNotNull);
        expect(tokenInfo?['tokenType'], equals('access'));
      });
    });

    group('🔒 اختبارات أمان API', () {
      test('فحص Rate Limiting', () async {
        const endpoint = '/test/endpoint';
        const userId = 'test_user_123';
        
        // الطلبات الأولى يجب أن تكون مسموحة
        for (int i = 0; i < 10; i++) {
          final isAllowed = await ApiSecurityService.checkRateLimit(endpoint, userId: userId);
          expect(isAllowed, isTrue);
        }
        
        // بعد عدد كبير من الطلبات، يجب أن يتم الرفض
        // (هذا يعتمد على إعدادات Rate Limiting)
      });

      test('تنظيف المدخلات من XSS', () {
        // مدخلات خطيرة
        const dangerousInput = '<script>alert("XSS")</script>';
        const sqlInjection = "'; DROP TABLE users; --";
        
        // تنظيف المدخلات
        final cleanXSS = ApiSecurityService.sanitizeInput(dangerousInput);
        final cleanSQL = ApiSecurityService.sanitizeInput(sqlInjection);
        
        // التحقق من إزالة الكود الخطير
        expect(cleanXSS, isNot(contains('<script>')));
        expect(cleanXSS, isNot(contains('alert')));
        expect(cleanSQL, isNot(contains('DROP TABLE')));
        expect(cleanSQL, isNot(contains('--')));
      });

      test('فحص صحة البريد الإلكتروني', () {
        // بريد إلكتروني صحيح
        expect(ApiSecurityService.isValidEmail('<EMAIL>'), isTrue);
        expect(ApiSecurityService.isValidEmail('<EMAIL>'), isTrue);
        
        // بريد إلكتروني غير صحيح
        expect(ApiSecurityService.isValidEmail('invalid-email'), isFalse);
        expect(ApiSecurityService.isValidEmail('test@'), isFalse);
        expect(ApiSecurityService.isValidEmail('@domain.com'), isFalse);
      });

      test('فحص صحة كلمة المرور', () {
        final validation = ApiSecurityService.validateInput(
          email: '<EMAIL>',
          password: 'StrongPass123!',
        );
        
        expect(validation.isValid, isTrue);
        expect(validation.errors, isEmpty);
        
        // كلمة مرور ضعيفة
        final weakValidation = ApiSecurityService.validateInput(
          email: '<EMAIL>',
          password: 'weak',
        );
        
        expect(weakValidation.isValid, isFalse);
        expect(weakValidation.errors, isNotEmpty);
      });
    });

    group('📱 اختبارات التكامل', () {
      test('اختبار أساسي للنظام', () {
        // اختبار بسيط للتأكد من تحميل الخدمات
        expect(EncryptionService.isPasswordStrong('TestPassword123!'), isTrue);
        expect(ApiSecurityService.isValidEmail('<EMAIL>'), isTrue);
      });
    });

    group('🔍 اختبارات الأداء', () {
      test('أداء التشفير', () {
        const testData = 'Test data for encryption performance';
        
        final stopwatch = Stopwatch()..start();
        
        // تشفير 10 مرات (تقليل العدد للاختبار)
        for (int i = 0; i < 10; i++) {
          final encrypted = EncryptionService.encryptSensitiveData(testData);
          final decrypted = EncryptionService.decryptSensitiveData(encrypted);
          expect(decrypted, equals(testData));
        }
        
        stopwatch.stop();
        
        // التحقق من أن العملية تتم في وقت معقول (أقل من 5 ثواني)
        expect(stopwatch.elapsedMilliseconds, lessThan(5000));
      });

      test('أداء JWT', () {
        final testUser = User(
          id: 'perf_test_user',
          email: '<EMAIL>',
          firstName: 'Performance',
          lastName: 'Test',
          isEmailVerified: true,
          role: UserRole.customer,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );
        
        final stopwatch = Stopwatch()..start();
        
        // توليد والتحقق من 10 توكن (تقليل العدد للاختبار)
        for (int i = 0; i < 10; i++) {
          final token = JWTService.generateAccessToken(testUser);
          final isValid = JWTService.verifyToken(token);
          expect(isValid, isTrue);
        }
        
        stopwatch.stop();
        
        // التحقق من الأداء (أقل من 3 ثواني)
        expect(stopwatch.elapsedMilliseconds, lessThan(3000));
      });
    });
  });
}
