import 'dart:convert';
import 'dart:math';
import 'package:crypto/crypto.dart';
import 'package:jwt_decoder/jwt_decoder.dart';
import 'package:flutter/foundation.dart';
import '../models/user_simple.dart' as user_model;

/// خدمة JWT للمصادقة الآمنة
class JWTService {
  // مفتاح سري للتوقيع (يجب تغييره في الإنتاج)
  static const String _secretKey = 'VisionLens2025JWTSecretKey!@#\$%^&*()';
  static const String _issuer = 'VisionLens';
  static const Duration _accessTokenExpiry = Duration(hours: 1);
  static const Duration _refreshTokenExpiry = Duration(days: 30);
  
  /// توليد JWT token للمستخدم
  static String generateAccessToken(user_model.User user) {
    try {
      final now = DateTime.now();
      final expiry = now.add(_accessTokenExpiry);
      
      final header = {
        'alg': 'HS256',
        'typ': 'JWT',
      };
      
      final payload = {
        'iss': _issuer,
        'sub': user.id,
        'aud': 'VisionLens-App',
        'exp': expiry.millisecondsSinceEpoch ~/ 1000,
        'iat': now.millisecondsSinceEpoch ~/ 1000,
        'nbf': now.millisecondsSinceEpoch ~/ 1000,
        'jti': _generateJTI(),
        'user': {
          'id': user.id,
          'email': user.email,
          'firstName': user.firstName,
          'lastName': user.lastName,
          'isEmailVerified': user.isEmailVerified,
          'role': _getUserRole(user),
        },
        'permissions': _getUserPermissions(user),
        'tokenType': 'access',
      };
      
      final token = _createJWT(header, payload);
      
      if (kDebugMode) {
        print('🔐 تم توليد Access Token للمستخدم: ${user.email}');
        print('⏰ صالح حتى: ${expiry.toLocal()}');
      }
      
      return token;
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في توليد Access Token: $e');
      }
      rethrow;
    }
  }
  
  /// توليد Refresh Token
  static String generateRefreshToken(user_model.User user) {
    try {
      final now = DateTime.now();
      final expiry = now.add(_refreshTokenExpiry);
      
      final header = {
        'alg': 'HS256',
        'typ': 'JWT',
      };
      
      final payload = {
        'iss': _issuer,
        'sub': user.id,
        'aud': 'VisionLens-App',
        'exp': expiry.millisecondsSinceEpoch ~/ 1000,
        'iat': now.millisecondsSinceEpoch ~/ 1000,
        'jti': _generateJTI(),
        'tokenType': 'refresh',
        'userId': user.id,
      };
      
      final token = _createJWT(header, payload);
      
      if (kDebugMode) {
        print('🔐 تم توليد Refresh Token للمستخدم: ${user.email}');
        print('⏰ صالح حتى: ${expiry.toLocal()}');
      }
      
      return token;
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في توليد Refresh Token: $e');
      }
      rethrow;
    }
  }
  
  /// التحقق من صحة JWT token
  static bool verifyToken(String token) {
    try {
      // فحص انتهاء الصلاحية
      if (JwtDecoder.isExpired(token)) {
        if (kDebugMode) {
          print('⚠️ Token منتهي الصلاحية');
        }
        return false;
      }
      
      // فحص التوقيع
      final parts = token.split('.');
      if (parts.length != 3) {
        if (kDebugMode) {
          print('❌ تنسيق Token غير صحيح');
        }
        return false;
      }
      
      final header = parts[0];
      final payload = parts[1];
      final signature = parts[2];
      
      final expectedSignature = _generateSignature('$header.$payload');
      
      if (signature != expectedSignature) {
        if (kDebugMode) {
          print('❌ توقيع Token غير صحيح');
        }
        return false;
      }
      
      // فحص المُصدر
      final decodedPayload = JwtDecoder.decode(token);
      if (decodedPayload['iss'] != _issuer) {
        if (kDebugMode) {
          print('❌ مُصدر Token غير صحيح');
        }
        return false;
      }
      
      if (kDebugMode) {
        print('✅ Token صحيح');
      }
      
      return true;
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في التحقق من Token: $e');
      }
      return false;
    }
  }
  
  /// استخراج بيانات المستخدم من Token
  static Map<String, dynamic>? getUserFromToken(String token) {
    try {
      if (!verifyToken(token)) {
        return null;
      }
      
      final decodedPayload = JwtDecoder.decode(token);
      return decodedPayload['user'];
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في استخراج بيانات المستخدم من Token: $e');
      }
      return null;
    }
  }
  
  /// استخراج الأذونات من Token
  static List<String>? getPermissionsFromToken(String token) {
    try {
      if (!verifyToken(token)) {
        return null;
      }
      
      final decodedPayload = JwtDecoder.decode(token);
      final permissions = decodedPayload['permissions'];
      
      if (permissions is List) {
        return permissions.cast<String>();
      }
      
      return [];
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في استخراج الأذونات من Token: $e');
      }
      return null;
    }
  }
  
  /// فحص انتهاء صلاحية Token
  static bool isTokenExpired(String token) {
    try {
      return JwtDecoder.isExpired(token);
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في فحص انتهاء صلاحية Token: $e');
      }
      return true;
    }
  }
  
  /// الحصول على وقت انتهاء صلاحية Token
  static DateTime? getTokenExpiry(String token) {
    try {
      final expiryDate = JwtDecoder.getExpirationDate(token);
      return expiryDate;
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في الحصول على وقت انتهاء الصلاحية: $e');
      }
      return null;
    }
  }
  
  /// فحص نوع Token (access أو refresh)
  static String? getTokenType(String token) {
    try {
      if (!verifyToken(token)) {
        return null;
      }
      
      final decodedPayload = JwtDecoder.decode(token);
      return decodedPayload['tokenType'];
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في فحص نوع Token: $e');
      }
      return null;
    }
  }
  
  /// تجديد Access Token باستخدام Refresh Token
  static String? refreshAccessToken(String refreshToken, user_model.User user) {
    try {
      // التحقق من صحة Refresh Token
      if (!verifyToken(refreshToken)) {
        if (kDebugMode) {
          print('❌ Refresh Token غير صحيح');
        }
        return null;
      }
      
      // التحقق من نوع Token
      final tokenType = getTokenType(refreshToken);
      if (tokenType != 'refresh') {
        if (kDebugMode) {
          print('❌ Token ليس من نوع refresh');
        }
        return null;
      }
      
      // توليد Access Token جديد
      final newAccessToken = generateAccessToken(user);
      
      if (kDebugMode) {
        print('🔄 تم تجديد Access Token للمستخدم: ${user.email}');
      }
      
      return newAccessToken;
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في تجديد Access Token: $e');
      }
      return null;
    }
  }
  
  /// إنشاء JWT token
  static String _createJWT(Map<String, dynamic> header, Map<String, dynamic> payload) {
    final encodedHeader = _base64UrlEncode(jsonEncode(header));
    final encodedPayload = _base64UrlEncode(jsonEncode(payload));
    final signature = _generateSignature('$encodedHeader.$encodedPayload');
    
    return '$encodedHeader.$encodedPayload.$signature';
  }
  
  /// توليد التوقيع
  static String _generateSignature(String data) {
    final key = utf8.encode(_secretKey);
    final bytes = utf8.encode(data);
    final hmac = Hmac(sha256, key);
    final digest = hmac.convert(bytes);
    
    return _base64UrlEncode(base64.encode(digest.bytes));
  }
  
  /// تشفير Base64 URL-safe
  static String _base64UrlEncode(String data) {
    final encoded = base64.encode(utf8.encode(data));
    return encoded.replaceAll('+', '-').replaceAll('/', '_').replaceAll('=', '');
  }
  
  /// توليد JWT ID فريد
  static String _generateJTI() {
    final random = Random.secure();
    final bytes = List<int>.generate(16, (i) => random.nextInt(256));
    return base64.encode(bytes).replaceAll('=', '');
  }
  
  /// تحديد دور المستخدم
  static String _getUserRole(user_model.User user) {
    // قائمة الإيميلات الإدارية
    const adminEmails = [
      '<EMAIL>',
      '<EMAIL>',
    ];
    
    if (adminEmails.contains(user.email.toLowerCase())) {
      return 'admin';
    }
    
    return 'user';
  }
  
  /// تحديد أذونات المستخدم
  static List<String> _getUserPermissions(user_model.User user) {
    final role = _getUserRole(user);
    
    switch (role) {
      case 'admin':
        return [
          'read:products',
          'write:products',
          'delete:products',
          'read:orders',
          'write:orders',
          'read:users',
          'write:users',
          'read:analytics',
          'manage:system',
        ];
      case 'user':
        return [
          'read:products',
          'write:cart',
          'write:orders',
          'read:own_orders',
          'write:reviews',
        ];
      default:
        return ['read:products'];
    }
  }
  
  /// فحص إذن معين
  static bool hasPermission(String token, String permission) {
    try {
      final permissions = getPermissionsFromToken(token);
      if (permissions == null) return false;
      
      return permissions.contains(permission);
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في فحص الإذن: $e');
      }
      return false;
    }
  }
  
  /// فحص إذا كان المستخدم إداري
  static bool isAdmin(String token) {
    try {
      final userData = getUserFromToken(token);
      if (userData == null) return false;
      
      return userData['role'] == 'admin';
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في فحص الصلاحية الإدارية: $e');
      }
      return false;
    }
  }
  
  /// إبطال Token (إضافة إلى القائمة السوداء)
  static Future<void> revokeToken(String token) async {
    try {
      // في التطبيق الحقيقي، يجب حفظ Token في قائمة سوداء
      // هنا سنحفظه محلياً كمثال
      
      if (kDebugMode) {
        print('🚫 تم إبطال Token');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في إبطال Token: $e');
      }
    }
  }
  
  /// الحصول على معلومات Token
  static Map<String, dynamic>? getTokenInfo(String token) {
    try {
      if (!verifyToken(token)) {
        return null;
      }
      
      final decodedPayload = JwtDecoder.decode(token);
      final expiry = getTokenExpiry(token);
      
      return {
        'isValid': true,
        'isExpired': isTokenExpired(token),
        'tokenType': decodedPayload['tokenType'],
        'userId': decodedPayload['sub'],
        'issuer': decodedPayload['iss'],
        'audience': decodedPayload['aud'],
        'issuedAt': DateTime.fromMillisecondsSinceEpoch(
          (decodedPayload['iat'] as int) * 1000,
        ),
        'expiresAt': expiry,
        'jwtId': decodedPayload['jti'],
        'permissions': decodedPayload['permissions'],
        'user': decodedPayload['user'],
      };
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في الحصول على معلومات Token: $e');
      }
      return {
        'isValid': false,
        'error': e.toString(),
      };
    }
  }
}
