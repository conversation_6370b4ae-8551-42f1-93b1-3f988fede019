import 'dart:typed_data';
import 'dart:io';
import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:image_picker/image_picker.dart';
import 'package:image/image.dart' as img;

/// خدمة معالجة الصور المحسنة
class ImageProcessingService {
  static DateTime? _lastCameraUse;
  static const Duration _cameraDelay = Duration(milliseconds: 1000); // زيادة التأخير
  static bool _isCameraInUse = false; // منع الاستخدام المتعدد
  static const int _maxImageSize = 10 * 1024 * 1024; // 10MB (مخفض للأداء)
  static const int _highQualityThreshold = 1 * 1024 * 1024; // 1MB
  static const int _mediumQualityThreshold = 3 * 1024 * 1024; // 3MB
  static const int _targetMaxSize = 2 * 1024 * 1024; // 2MB حد أقصى للمعالجة
  
  /// اختيار صورة من المعرض مع ضغط فوري
  static Future<ImageResult?> pickImageFromGallery({
    bool allowMultiple = false,
    int? maxImages = 10,
  }) async {
    try {
      if (kDebugMode) {
        print('📸 اختيار صورة من المعرض مع ضغط فوري...');
      }

      final ImagePicker picker = ImagePicker();

      if (allowMultiple) {
        final List<XFile> images = await picker.pickMultiImage(
          imageQuality: 70, // جودة متوسطة لتقليل الحجم
          maxWidth: 2000,   // حد أقصى للعرض
          maxHeight: 2000,  // حد أقصى للارتفاع
          limit: maxImages,
        );
        
        if (images.isNotEmpty) {
          final List<ProcessedImage> processedImages = [];
          
          for (final image in images) {
            final processed = await _processXFile(image);
            if (processed != null) {
              processedImages.add(processed);
            }
          }
          
          return ImageResult(
            images: processedImages,
            isMultiple: true,
          );
        }
      } else {
        final XFile? image = await picker.pickImage(
          source: ImageSource.gallery,
          imageQuality: 80,  // جودة عالية ولكن معقولة
          maxWidth: 2000,    // حد أقصى للعرض
          maxHeight: 2000,   // حد أقصى للارتفاع
        );
        
        if (image != null) {
          final processed = await _processXFile(image);
          if (processed != null) {
            return ImageResult(
              images: [processed],
              isMultiple: false,
            );
          }
        }
      }
      
      return null;
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في اختيار الصورة من المعرض: $e');
      }
      return null;
    }
  }
  
  /// اختيار صورة من الكاميرا مع ضغط فوري
  static Future<ImageResult?> pickImageFromCamera() async {
    // منع الاستخدام المتعدد للكاميرا
    if (_isCameraInUse) {
      if (kDebugMode) {
        print('⚠️ الكاميرا قيد الاستخدام بالفعل، يرجى الانتظار...');
      }
      return null;
    }

    ImagePicker? picker;
    try {
      _isCameraInUse = true; // تعيين حالة الاستخدام

      if (kDebugMode) {
        print('📷 التقاط صورة من الكاميرا مع ضغط فوري...');
      }

      // التحقق من التأخير بين استخدامات الكاميرا
      if (_lastCameraUse != null) {
        final timeSinceLastUse = DateTime.now().difference(_lastCameraUse!);
        if (timeSinceLastUse < _cameraDelay) {
          final remainingDelay = _cameraDelay - timeSinceLastUse;
          if (kDebugMode) {
            print('⏳ انتظار ${remainingDelay.inMilliseconds}ms قبل استخدام الكاميرا مرة أخرى...');
          }
          await Future.delayed(remainingDelay);
        }
      }

      picker = ImagePicker();
      _lastCameraUse = DateTime.now();

      // إضافة timeout لتجنب التعليق
      final XFile? image = await picker.pickImage(
        source: ImageSource.camera,
        imageQuality: 60,  // تقليل الجودة أكثر لتجنب مشاكل الذاكرة
        maxWidth: 1200,    // تقليل الحد الأقصى للعرض أكثر
        maxHeight: 1200,   // تقليل الحد الأقصى للارتفاع أكثر
        preferredCameraDevice: CameraDevice.rear, // الكاميرا الخلفية للجودة الأفضل
      ).timeout(
        const Duration(seconds: 30),
        onTimeout: () {
          if (kDebugMode) {
            print('⏰ انتهت مهلة التقاط الصورة');
          }
          return null;
        },
      );

      if (image != null) {
        if (kDebugMode) {
          print('📷 تم التقاط الصورة بنجاح، بدء المعالجة...');
        }

        // معالجة الصورة في background isolate
        final processed = await _processXFileAsync(image);
        if (processed != null) {
          return ImageResult(
            images: [processed],
            isMultiple: false,
          );
        }
      } else {
        if (kDebugMode) {
          print('📷 لم يتم التقاط أي صورة');
        }
      }

      return null;
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في التقاط الصورة من الكاميرا: $e');
        print('❌ Stack trace: ${StackTrace.current}');
      }
      return null;
    } finally {
      // تنظيف الموارد
      picker = null;
      _isCameraInUse = false; // إلغاء حالة الاستخدام

      if (kDebugMode) {
        print('🧹 تم تنظيف موارد الكاميرا');
      }

      // إجبار garbage collection
      await Future.delayed(const Duration(milliseconds: 100));
    }
  }
  
  /// معالجة XFile وتحويلها إلى ProcessedImage (نسخة محسنة للكاميرا)
  static Future<ProcessedImage?> _processXFileAsync(XFile xFile) async {
    try {
      if (kDebugMode) {
        print('📊 بدء معالجة الصورة بشكل غير متزامن: ${xFile.name}');
      }

      // قراءة البيانات مع timeout
      final Uint8List originalBytes = await xFile.readAsBytes().timeout(
        const Duration(seconds: 10),
        onTimeout: () {
          if (kDebugMode) {
            print('⏰ انتهت مهلة قراءة الصورة');
          }
          throw TimeoutException('انتهت مهلة قراءة الصورة', const Duration(seconds: 10));
        },
      );

      final String name = xFile.name;
      final String path = xFile.path;

      if (kDebugMode) {
        print('📏 الحجم الأصلي: ${originalBytes.length} بايت');
      }

      // التحقق من صحة الصورة
      if (!_isValidImage(originalBytes, name)) {
        if (kDebugMode) {
          print('❌ الصورة غير صالحة: $name');
        }
        return null;
      }

      // ضغط الصورة إذا كانت كبيرة جداً
      Uint8List processedBytes = originalBytes;
      if (originalBytes.length > 2 * 1024 * 1024) { // أكبر من 2MB
        if (kDebugMode) {
          print('🗜️ ضغط الصورة الكبيرة...');
        }
        processedBytes = await ImageProcessingServiceExtension._compressImageBytes(originalBytes);
      }

      if (kDebugMode) {
        print('✅ الصورة جاهزة للاستخدام: ${processedBytes.length} بايت');
      }

      // الحصول على معلومات الصورة
      final imageInfo = await _getImageInfo(processedBytes);

      return ProcessedImage(
        bytes: processedBytes,
        name: name,
        path: path,
        width: imageInfo.width,
        height: imageInfo.height,
        size: processedBytes.length,
        format: imageInfo.format,
        quality: ImageQuality.medium, // جودة متوسطة للكاميرا
      );
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في معالجة الصورة: $e');
      }
      return null;
    }
  }

  /// معالجة XFile وتحويلها إلى ProcessedImage (النسخة الأصلية)
  static Future<ProcessedImage?> _processXFile(XFile xFile) async {
    try {
      if (kDebugMode) {
        print('📊 بدء معالجة الصورة: ${xFile.name}');
      }

      // قراءة البيانات في background isolate لتجنب تعليق UI
      final Uint8List originalBytes = await xFile.readAsBytes();
      final String name = xFile.name;
      final String path = xFile.path;

      if (kDebugMode) {
        print('📏 الحجم الأصلي: ${originalBytes.length} بايت');
      }

      // التحقق من صحة الصورة
      if (!_isValidImage(originalBytes, name)) {
        if (kDebugMode) {
          print('❌ الصورة غير صالحة: $name');
        }
        return null;
      }

      // الصورة مضغوطة مسبقاً من ImagePicker، لا حاجة لضغط إضافي
      Uint8List processedBytes = originalBytes;

      if (kDebugMode) {
        print('✅ الصورة جاهزة للاستخدام: ${processedBytes.length} بايت');
      }

      // الحصول على معلومات الصورة
      final imageInfo = await _getImageInfo(processedBytes);

      return ProcessedImage(
        bytes: processedBytes,
        name: name,
        path: path,
        size: processedBytes.length,
        width: imageInfo.width,
        height: imageInfo.height,
        format: imageInfo.format,
        quality: _calculateQuality(processedBytes.length),
      );
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في معالجة الصورة: $e');
      }
      return null;
    }
  }

  /// ضغط الصورة في background isolate
  static Future<Uint8List> _compressImageInBackground(Uint8List bytes) async {
    try {
      // استخدام compute لتشغيل الضغط في isolate منفصل
      return await compute(_compressImageIsolate, {
        'bytes': bytes,
        'targetSize': _targetMaxSize,
      });
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في ضغط الصورة: $e');
      }
      return bytes; // إرجاع الصورة الأصلية في حالة الخطأ
    }
  }

  /// دالة الضغط التي تعمل في isolate منفصل
  static Uint8List _compressImageIsolate(Map<String, dynamic> params) {
    final Uint8List bytes = params['bytes'];
    final int targetSize = params['targetSize'];

    try {
      final image = img.decodeImage(bytes);
      if (image == null) return bytes;

      // حساب نسبة التصغير المطلوبة بناءً على الأبعاد والحجم
      double scaleFactor = 1.0;

      // تصغير بناءً على الأبعاد أولاً
      const int maxDimension = 4000; // حد أقصى للبعد الواحد
      if (image.width > maxDimension || image.height > maxDimension) {
        final widthScale = maxDimension / image.width;
        final heightScale = maxDimension / image.height;
        scaleFactor = widthScale < heightScale ? widthScale : heightScale;
      }

      // تصغير إضافي بناءً على الحجم
      if (bytes.length > targetSize) {
        final sizeScale = (targetSize / bytes.length).clamp(0.1, 1.0);
        scaleFactor = scaleFactor * sizeScale;
      }

      // التأكد من أن النسبة معقولة
      scaleFactor = scaleFactor.clamp(0.1, 1.0);

      // تصغير الصورة
      final newWidth = (image.width * scaleFactor).round().clamp(100, 4000);
      final newHeight = (image.height * scaleFactor).round().clamp(100, 4000);

      final resizedImage = img.copyResize(
        image,
        width: newWidth,
        height: newHeight,
        interpolation: img.Interpolation.cubic,
      );

      // ضغط بجودة متوسطة للصور الكبيرة
      final quality = scaleFactor > 0.5 ? 85 : 75;
      final compressedBytes = img.encodeJpg(resizedImage, quality: quality);

      return Uint8List.fromList(compressedBytes);
    } catch (e) {
      return bytes; // إرجاع الصورة الأصلية في حالة الخطأ
    }
  }
  
  /// التحقق من صحة الصورة
  static bool _isValidImage(Uint8List bytes, String fileName) {
    // التحقق من الحجم (سنقوم بضغط الصور الكبيرة بدلاً من رفضها)
    if (bytes.length > 50 * 1024 * 1024) { // 50MB حد أقصى مطلق
      if (kDebugMode) {
        print('❌ الصورة كبيرة جداً جداً: ${bytes.length} بايت');
      }
      return false;
    }
    
    // التحقق من الامتداد
    final extension = fileName.split('.').last.toLowerCase();
    final validExtensions = ['png', 'jpg', 'jpeg', 'gif', 'webp', 'bmp', 'tiff'];
    if (!validExtensions.contains(extension)) {
      if (kDebugMode) {
        print('❌ امتداد الصورة غير مدعوم: $extension');
      }
      return false;
    }
    
    // التحقق من أن البيانات صورة فعلاً
    try {
      final image = img.decodeImage(bytes);
      if (image == null) {
        if (kDebugMode) {
          print('❌ فشل في فك تشفير الصورة');
        }
        return false;
      }
      
      // التحقق من الأبعاد المعقولة
      if (image.width < 10 || image.height < 10) {
        if (kDebugMode) {
          print('❌ أبعاد الصورة صغيرة جداً: ${image.width}x${image.height}');
        }
        return false;
      }

      // الآن الصور مضغوطة مسبقاً، الأبعاد يجب أن تكون معقولة
      if (image.width > 3000 || image.height > 3000) {
        if (kDebugMode) {
          print('⚠️ أبعاد الصورة كبيرة: ${image.width}x${image.height} - ولكن مقبولة');
        }
      }
      
      return true;
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في التحقق من الصورة: $e');
      }
      return false;
    }
  }
  
  /// الحصول على معلومات الصورة
  static Future<ImageInfo> _getImageInfo(Uint8List bytes) async {
    try {
      final image = img.decodeImage(bytes);
      if (image != null) {
        return ImageInfo(
          width: image.width,
          height: image.height,
          format: _detectImageFormat(bytes),
        );
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في الحصول على معلومات الصورة: $e');
      }
    }
    
    return ImageInfo(width: 0, height: 0, format: 'unknown');
  }
  
  /// تحديد تنسيق الصورة
  static String _detectImageFormat(Uint8List bytes) {
    if (bytes.length < 4) return 'unknown';
    
    // PNG signature
    if (bytes[0] == 0x89 && bytes[1] == 0x50 && bytes[2] == 0x4E && bytes[3] == 0x47) {
      return 'PNG';
    }
    
    // JPEG signature
    if (bytes[0] == 0xFF && bytes[1] == 0xD8) {
      return 'JPEG';
    }
    
    // GIF signature
    if (bytes[0] == 0x47 && bytes[1] == 0x49 && bytes[2] == 0x46) {
      return 'GIF';
    }
    
    // WebP signature
    if (bytes.length >= 12 && 
        bytes[0] == 0x52 && bytes[1] == 0x49 && bytes[2] == 0x46 && bytes[3] == 0x46 &&
        bytes[8] == 0x57 && bytes[9] == 0x45 && bytes[10] == 0x42 && bytes[11] == 0x50) {
      return 'WebP';
    }
    
    return 'unknown';
  }
  
  /// حساب جودة الصورة بناءً على الحجم
  static ImageQuality _calculateQuality(int sizeInBytes) {
    if (sizeInBytes <= _highQualityThreshold) {
      return ImageQuality.high;
    } else if (sizeInBytes <= _mediumQualityThreshold) {
      return ImageQuality.medium;
    } else {
      return ImageQuality.low;
    }
  }
  
  /// ضغط الصورة مع الحفاظ على الجودة
  static Future<Uint8List> compressImage(
    Uint8List bytes, {
    int? maxWidth,
    int? maxHeight,
    int quality = 85,
    String format = 'JPEG',
  }) async {
    try {
      if (kDebugMode) {
        print('🔄 ضغط الصورة...');
        print('📏 الحجم الأصلي: ${bytes.length} بايت');
      }
      
      final image = img.decodeImage(bytes);
      if (image == null) {
        if (kDebugMode) {
          print('❌ فشل في فك تشفير الصورة للضغط');
        }
        return bytes;
      }
      
      img.Image processedImage = image;
      
      // تغيير الحجم إذا لزم الأمر
      if (maxWidth != null || maxHeight != null) {
        final newWidth = maxWidth ?? image.width;
        final newHeight = maxHeight ?? image.height;
        
        if (image.width > newWidth || image.height > newHeight) {
          processedImage = img.copyResize(
            image,
            width: newWidth,
            height: newHeight,
            interpolation: img.Interpolation.cubic, // أفضل جودة للتغيير
          );
          
          if (kDebugMode) {
            print('📐 تم تغيير الحجم: ${image.width}x${image.height} → ${processedImage.width}x${processedImage.height}');
          }
        }
      }
      
      // ضغط الصورة
      List<int> compressedBytes;
      
      switch (format.toUpperCase()) {
        case 'PNG':
          compressedBytes = img.encodePng(processedImage);
          break;
        case 'WEBP':
          // WebP encoding is not available in the image package, use JPEG instead
          compressedBytes = img.encodeJpg(processedImage, quality: quality);
          break;
        case 'JPEG':
        default:
          compressedBytes = img.encodeJpg(processedImage, quality: quality);
          break;
      }
      
      final result = Uint8List.fromList(compressedBytes);
      
      if (kDebugMode) {
        print('✅ تم ضغط الصورة: ${bytes.length} → ${result.length} بايت');
        print('📊 نسبة الضغط: ${((1 - result.length / bytes.length) * 100).toStringAsFixed(1)}%');
      }
      
      return result;
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في ضغط الصورة: $e');
      }
      return bytes;
    }
  }
  
  /// تحسين الصورة للعرض
  static Future<Uint8List> optimizeForDisplay(
    Uint8List bytes, {
    int maxDisplayWidth = 1920,
    int maxDisplayHeight = 1080,
  }) async {
    try {
      final image = img.decodeImage(bytes);
      if (image == null) return bytes;
      
      // إذا كانت الصورة أصغر من الحد الأقصى، لا تغيرها
      if (image.width <= maxDisplayWidth && image.height <= maxDisplayHeight) {
        return bytes;
      }
      
      // حساب النسبة للحفاظ على الأبعاد
      final widthRatio = maxDisplayWidth / image.width;
      final heightRatio = maxDisplayHeight / image.height;
      final ratio = widthRatio < heightRatio ? widthRatio : heightRatio;
      
      final newWidth = (image.width * ratio).round();
      final newHeight = (image.height * ratio).round();
      
      return await compressImage(
        bytes,
        maxWidth: newWidth,
        maxHeight: newHeight,
        quality: 90, // جودة عالية للعرض
      );
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في تحسين الصورة للعرض: $e');
      }
      return bytes;
    }
  }
}

/// نتيجة اختيار الصورة
class ImageResult {
  final List<ProcessedImage> images;
  final bool isMultiple;
  
  const ImageResult({
    required this.images,
    required this.isMultiple,
  });
  
  ProcessedImage? get firstImage => images.isNotEmpty ? images.first : null;
  int get count => images.length;
  bool get isEmpty => images.isEmpty;
  bool get isNotEmpty => images.isNotEmpty;
}

/// صورة معالجة
class ProcessedImage {
  final Uint8List bytes;
  final String name;
  final String path;
  final int size;
  final int width;
  final int height;
  final String format;
  final ImageQuality quality;
  
  const ProcessedImage({
    required this.bytes,
    required this.name,
    required this.path,
    required this.size,
    required this.width,
    required this.height,
    required this.format,
    required this.quality,
  });
  
  String get sizeFormatted {
    if (size < 1024) return '$size B';
    if (size < 1024 * 1024) return '${(size / 1024).toStringAsFixed(1)} KB';
    return '${(size / (1024 * 1024)).toStringAsFixed(1)} MB';
  }
  
  String get dimensions => '${width}x$height';
  
  double get aspectRatio => width / height;
  
  @override
  String toString() {
    return 'ProcessedImage(name: $name, size: $sizeFormatted, dimensions: $dimensions, format: $format, quality: $quality)';
  }

  /// الحصول على تنسيق الصورة من اسم الملف
  static String _getImageFormat(String fileName) {
    final extension = fileName.toLowerCase().split('.').last;
    switch (extension) {
      case 'jpg':
      case 'jpeg':
        return 'JPEG';
      case 'png':
        return 'PNG';
      case 'gif':
        return 'GIF';
      case 'webp':
        return 'WebP';
      case 'bmp':
        return 'BMP';
      default:
        return 'Unknown';
    }
  }

  /// حساب جودة الصورة بناءً على الحجم
  static ImageQuality _calculateQuality(int sizeInBytes) {
    if (sizeInBytes > 2 * 1024 * 1024) { // أكبر من 2MB
      return ImageQuality.high;
    } else if (sizeInBytes > 500 * 1024) { // أكبر من 500KB
      return ImageQuality.medium;
    } else {
      return ImageQuality.low;
    }
  }
}

/// معلومات الصورة
class ImageInfo {
  final int width;
  final int height;
  final String format;
  
  const ImageInfo({
    required this.width,
    required this.height,
    required this.format,
  });
}

/// جودة الصورة
enum ImageQuality {
  high,
  medium,
  low,
}

extension ImageQualityExtension on ImageQuality {
  String get name {
    switch (this) {
      case ImageQuality.high:
        return 'عالية';
      case ImageQuality.medium:
        return 'متوسطة';
      case ImageQuality.low:
        return 'منخفضة';
    }
  }

  int get compressionQuality {
    switch (this) {
      case ImageQuality.high:
        return 95;
      case ImageQuality.medium:
        return 85;
      case ImageQuality.low:
        return 70;
    }
  }
}

/// إضافة دالة ضغط الصور للحالات الكبيرة
extension ImageProcessingServiceExtension on ImageProcessingService {
  /// ضغط الصورة الكبيرة
  static Future<Uint8List> _compressImageBytes(Uint8List originalBytes) async {
    try {
      if (kDebugMode) {
        print('🗜️ بدء ضغط الصورة الكبيرة...');
      }

      // فك تشفير الصورة
      final img.Image? image = img.decodeImage(originalBytes);
      if (image == null) {
        if (kDebugMode) {
          print('❌ فشل في فك تشفير الصورة للضغط');
        }
        return originalBytes;
      }

      // تقليل الحجم إذا كان كبيراً جداً
      img.Image resizedImage = image;
      if (image.width > 1200 || image.height > 1200) {
        final double ratio = 1200 / (image.width > image.height ? image.width : image.height);
        final int newWidth = (image.width * ratio).round();
        final int newHeight = (image.height * ratio).round();

        resizedImage = img.copyResize(image, width: newWidth, height: newHeight);

        if (kDebugMode) {
          print('📏 تم تغيير حجم الصورة من ${image.width}x${image.height} إلى ${newWidth}x${newHeight}');
        }
      }

      // ضغط الصورة
      final Uint8List compressedBytes = Uint8List.fromList(
        img.encodeJpg(resizedImage, quality: 75)
      );

      if (kDebugMode) {
        print('✅ تم ضغط الصورة من ${originalBytes.length} إلى ${compressedBytes.length} بايت');
      }

      return compressedBytes;
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في ضغط الصورة: $e');
      }
      return originalBytes; // إرجاع الصورة الأصلية في حالة الخطأ
    }
  }
}
