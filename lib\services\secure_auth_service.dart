import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/user_simple.dart' as user_model;
import 'encryption_service.dart';
import 'secure_storage_service.dart';
import 'jwt_service.dart';
import 'firebase_mock_service.dart';

/// خدمة المصادقة الآمنة مع تشفير وJWT
class SecureAuthService {
  static bool _isInitialized = false;
  static user_model.User? _currentUser;
  static String? _accessToken;
  static String? _refreshToken;
  
  /// تهيئة الخدمة
  static Future<void> initialize() async {
    try {
      if (_isInitialized) return;
      
      if (kDebugMode) {
        print('🔐 تهيئة خدمة المصادقة الآمنة...');
      }
      
      // تهيئة الخدمات المطلوبة
      await EncryptionService.initialize();
      await SecureStorageService.initialize();
      
      // ترحيل البيانات القديمة
      await SecureStorageService.migrateOldData();
      
      // تحميل حالة المصادقة المحفوظة
      await _loadAuthState();
      
      _isInitialized = true;
      
      if (kDebugMode) {
        print('✅ تم تهيئة خدمة المصادقة الآمنة بنجاح');
        if (_currentUser != null) {
          print('👤 مستخدم محفوظ: ${_currentUser!.email}');
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في تهيئة خدمة المصادقة الآمنة: $e');
      }
      rethrow;
    }
  }
  
  /// تسجيل الدخول بالإيميل وكلمة المرور
  static Future<AuthResult> signInWithEmailAndPassword({
    required String email,
    required String password,
  }) async {
    try {
      if (!_isInitialized) await initialize();
      
      if (kDebugMode) {
        print('🔐 محاولة تسجيل الدخول: $email');
      }
      
      // التحقق من صحة البيانات
      if (email.isEmpty || password.isEmpty) {
        return AuthResult.failure('يرجى إدخال البريد الإلكتروني وكلمة المرور');
      }
      
      // محاولة تسجيل الدخول عبر Firebase Mock
      final mockResult = await FirebaseMockService.signInWithEmailAndPassword(
        email: email,
        password: password,
      );
      
      if (mockResult.isSuccess && mockResult.user != null) {
        // حفظ المستخدم وتوليد Tokens
        await _setCurrentUser(mockResult.user!);
        
        if (kDebugMode) {
          print('✅ تم تسجيل الدخول بنجاح: ${mockResult.user!.email}');
        }
        
        return AuthResult.success(mockResult.user!);
      } else {
        return AuthResult.failure(mockResult.errorMessage ?? 'فشل في تسجيل الدخول');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في تسجيل الدخول: $e');
      }
      return AuthResult.failure('حدث خطأ أثناء تسجيل الدخول');
    }
  }
  
  /// إنشاء حساب جديد
  static Future<AuthResult> createUserWithEmailAndPassword({
    required String email,
    required String password,
    required String firstName,
    required String lastName,
    String? phone,
  }) async {
    try {
      if (!_isInitialized) await initialize();
      
      if (kDebugMode) {
        print('🔐 محاولة إنشاء حساب جديد: $email');
      }
      
      // التحقق من قوة كلمة المرور
      if (!EncryptionService.isPasswordStrong(password)) {
        return AuthResult.failure(
          'كلمة المرور ضعيفة. يجب أن تحتوي على 8 أحرف على الأقل، حرف كبير، حرف صغير، رقم، ورمز خاص',
        );
      }
      
      // تشفير كلمة المرور
      final hashedPassword = EncryptionService.hashPassword(password);
      
      // إنشاء الحساب عبر Firebase Mock
      final mockResult = await FirebaseMockService.createUserWithEmailAndPassword(
        email: email,
        password: hashedPassword, // حفظ كلمة المرور مشفرة
        firstName: firstName,
        lastName: lastName,
        phone: phone,
      );
      
      if (mockResult.isSuccess && mockResult.user != null) {
        // حفظ المستخدم وتوليد Tokens
        await _setCurrentUser(mockResult.user!);
        
        if (kDebugMode) {
          print('✅ تم إنشاء الحساب بنجاح: ${mockResult.user!.email}');
        }
        
        return AuthResult.success(mockResult.user!);
      } else {
        return AuthResult.failure(mockResult.errorMessage ?? 'فشل في إنشاء الحساب');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في إنشاء الحساب: $e');
      }
      return AuthResult.failure('حدث خطأ أثناء إنشاء الحساب');
    }
  }
  
  /// تسجيل الدخول بـ Google
  static Future<AuthResult> signInWithGoogle() async {
    try {
      if (!_isInitialized) await initialize();
      
      if (kDebugMode) {
        print('🔐 محاولة تسجيل الدخول بـ Google...');
      }
      
      // تسجيل الدخول عبر Google
      final mockResult = await FirebaseMockService.signInWithGoogle();

      if (mockResult.isSuccess && mockResult.user != null) {
        // حفظ المستخدم وتوليد Tokens
        await _setCurrentUser(mockResult.user!);

        if (kDebugMode) {
          print('✅ تم تسجيل الدخول بـ Google بنجاح: ${mockResult.user!.email}');
        }

        return AuthResult.success(mockResult.user!);
      } else {
        return AuthResult.failure(mockResult.errorMessage ?? 'فشل في تسجيل الدخول بـ Google');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في تسجيل الدخول بـ Google: $e');
      }
      return AuthResult.failure('حدث خطأ أثناء تسجيل الدخول بـ Google');
    }
  }
  
  /// تسجيل الخروج
  static Future<bool> signOut() async {
    try {
      if (kDebugMode) {
        print('🔐 تسجيل الخروج...');
      }
      
      // إبطال Tokens
      if (_accessToken != null) {
        await JWTService.revokeToken(_accessToken!);
      }
      if (_refreshToken != null) {
        await JWTService.revokeToken(_refreshToken!);
      }
      
      // مسح البيانات المحلية
      await _clearAuthState();
      
      // تسجيل الخروج من Firebase Mock
      await FirebaseMockService.signOut();
      
      if (kDebugMode) {
        print('✅ تم تسجيل الخروج بنجاح');
      }
      
      return true;
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في تسجيل الخروج: $e');
      }
      return false;
    }
  }
  
  /// تجديد Access Token
  static Future<bool> refreshAccessToken() async {
    try {
      if (_refreshToken == null || _currentUser == null) {
        if (kDebugMode) {
          print('⚠️ لا يوجد Refresh Token أو مستخدم');
        }
        return false;
      }
      
      // تجديد Access Token
      final newAccessToken = JWTService.refreshAccessToken(_refreshToken!, _currentUser!);
      
      if (newAccessToken != null) {
        _accessToken = newAccessToken;
        await SecureStorageService.saveAuthToken(newAccessToken);
        
        if (kDebugMode) {
          print('✅ تم تجديد Access Token بنجاح');
        }
        
        return true;
      } else {
        if (kDebugMode) {
          print('❌ فشل في تجديد Access Token');
        }
        
        // إذا فشل التجديد، قم بتسجيل الخروج
        await signOut();
        return false;
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في تجديد Access Token: $e');
      }
      return false;
    }
  }
  
  /// التحقق من صحة المصادقة
  static Future<bool> isAuthenticated() async {
    try {
      if (_currentUser == null || _accessToken == null) {
        return false;
      }
      
      // فحص صحة Access Token
      if (JWTService.verifyToken(_accessToken!)) {
        return true;
      }
      
      // إذا كان Access Token منتهي الصلاحية، حاول التجديد
      if (JWTService.isTokenExpired(_accessToken!)) {
        return await refreshAccessToken();
      }
      
      return false;
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في التحقق من المصادقة: $e');
      }
      return false;
    }
  }
  
  /// الحصول على المستخدم الحالي
  static user_model.User? get currentUser => _currentUser;
  
  /// الحصول على Access Token
  static String? get accessToken => _accessToken;
  
  /// الحصول على Refresh Token
  static String? get refreshToken => _refreshToken;
  
  /// فحص إذا كان المستخدم إداري
  static bool get isAdmin {
    if (_accessToken == null) return false;
    return JWTService.isAdmin(_accessToken!);
  }
  
  /// فحص إذن معين
  static bool hasPermission(String permission) {
    if (_accessToken == null) return false;
    return JWTService.hasPermission(_accessToken!, permission);
  }
  
  /// تعيين المستخدم الحالي وتوليد Tokens
  static Future<void> _setCurrentUser(user_model.User user) async {
    try {
      _currentUser = user;
      
      // توليد JWT Tokens
      _accessToken = JWTService.generateAccessToken(user);
      _refreshToken = JWTService.generateRefreshToken(user);
      
      // حفظ البيانات مشفرة
      await SecureStorageService.saveUser(user);
      await SecureStorageService.saveAuthToken(_accessToken!);
      
      // حفظ Refresh Token منفصل
      final prefs = await SharedPreferences.getInstance();
      final encryptedRefreshToken = EncryptionService.encryptSensitiveData(_refreshToken!);
      await prefs.setString('visionlens_secure_refresh_token', encryptedRefreshToken);
      
      if (kDebugMode) {
        print('✅ تم حفظ بيانات المستخدم والTokens بأمان');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في حفظ بيانات المستخدم: $e');
      }
      rethrow;
    }
  }
  
  /// تحميل حالة المصادقة المحفوظة
  static Future<void> _loadAuthState() async {
    try {
      // تحميل بيانات المستخدم
      _currentUser = await SecureStorageService.loadUser();
      
      // تحميل Access Token
      _accessToken = await SecureStorageService.loadAuthToken();
      
      // تحميل Refresh Token
      final prefs = await SharedPreferences.getInstance();
      final encryptedRefreshToken = prefs.getString('visionlens_secure_refresh_token');
      if (encryptedRefreshToken != null) {
        try {
          _refreshToken = EncryptionService.decryptSensitiveData(encryptedRefreshToken);
        } catch (e) {
          if (kDebugMode) {
            print('⚠️ فشل في فك تشفير Refresh Token: $e');
          }
        }
      }
      
      // التحقق من صحة البيانات المحملة
      if (_currentUser != null && _accessToken != null) {
        final isValid = await isAuthenticated();
        if (!isValid) {
          await _clearAuthState();
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في تحميل حالة المصادقة: $e');
      }
      await _clearAuthState();
    }
  }
  
  /// مسح حالة المصادقة
  static Future<void> _clearAuthState() async {
    try {
      _currentUser = null;
      _accessToken = null;
      _refreshToken = null;
      
      // مسح البيانات المحفوظة
      await SecureStorageService.clearUser();
      await SecureStorageService.clearAuthToken();
      
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove('visionlens_secure_refresh_token');
      
      if (kDebugMode) {
        print('🗑️ تم مسح حالة المصادقة');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في مسح حالة المصادقة: $e');
      }
    }
  }
  
  /// تغيير كلمة المرور
  static Future<bool> changePassword({
    required String currentPassword,
    required String newPassword,
  }) async {
    try {
      if (_currentUser == null) {
        return false;
      }
      
      // التحقق من قوة كلمة المرور الجديدة
      if (!EncryptionService.isPasswordStrong(newPassword)) {
        if (kDebugMode) {
          print('❌ كلمة المرور الجديدة ضعيفة');
        }
        return false;
      }
      
      // تشفير كلمة المرور الجديدة
      final hashedNewPassword = EncryptionService.hashPassword(newPassword);
      
      // تحديث كلمة المرور في النظام
      // هنا يجب إضافة منطق تحديث كلمة المرور في قاعدة البيانات
      
      if (kDebugMode) {
        print('✅ تم تغيير كلمة المرور بنجاح');
      }
      
      return true;
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في تغيير كلمة المرور: $e');
      }
      return false;
    }
  }
  
  /// الحصول على معلومات الأمان
  static Future<Map<String, dynamic>> getSecurityInfo() async {
    try {
      final securityStats = await SecureStorageService.getSecurityStats();
      
      Map<String, dynamic>? tokenInfo;
      if (_accessToken != null) {
        tokenInfo = JWTService.getTokenInfo(_accessToken!);
      }
      
      return {
        'isAuthenticated': await isAuthenticated(),
        'currentUser': _currentUser?.toJson(),
        'isAdmin': isAdmin,
        'tokenInfo': tokenInfo,
        'securityStats': securityStats,
        'lastSecurityCheck': DateTime.now().toIso8601String(),
      };
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في الحصول على معلومات الأمان: $e');
      }
      return {};
    }
  }
}

/// نتيجة عملية المصادقة
class AuthResult {
  final bool isSuccess;
  final user_model.User? user;
  final String? errorMessage;
  
  const AuthResult._({
    required this.isSuccess,
    this.user,
    this.errorMessage,
  });
  
  factory AuthResult.success(user_model.User user) {
    return AuthResult._(isSuccess: true, user: user);
  }
  
  factory AuthResult.failure(String errorMessage) {
    return AuthResult._(isSuccess: false, errorMessage: errorMessage);
  }
}
