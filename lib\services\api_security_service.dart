import 'dart:convert';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'jwt_service.dart';
import 'secure_auth_service.dart';

/// خدمة حماية API مع Rate Limiting وValidation
class ApiSecurityService {
  static const int _maxRequestsPerMinute = 60;
  static const int _maxRequestsPerHour = 1000;
  static const Duration _rateLimitWindow = Duration(minutes: 1);
  
  static final Map<String, List<DateTime>> _requestHistory = {};
  static final Map<String, int> _failedAttempts = {};
  static const int _maxFailedAttempts = 5;
  static const Duration _lockoutDuration = Duration(minutes: 15);
  
  /// فحص Rate Limiting
  static Future<bool> checkRateLimit(String endpoint, {String? userId}) async {
    try {
      final key = userId != null ? '${endpoint}_$userId' : endpoint;
      final now = DateTime.now();
      
      // إنشاء قائمة الطلبات إذا لم تكن موجودة
      _requestHistory[key] ??= [];
      
      // إزالة الطلبات القديمة (خارج النافزة الزمنية)
      _requestHistory[key]!.removeWhere(
        (requestTime) => now.difference(requestTime) > _rateLimitWindow,
      );
      
      // فحص عدد الطلبات
      if (_requestHistory[key]!.length >= _maxRequestsPerMinute) {
        if (kDebugMode) {
          print('🚫 تم تجاوز حد الطلبات للـ endpoint: $endpoint');
        }
        return false;
      }
      
      // إضافة الطلب الحالي
      _requestHistory[key]!.add(now);
      
      if (kDebugMode) {
        print('✅ طلب مسموح للـ endpoint: $endpoint (${_requestHistory[key]!.length}/$_maxRequestsPerMinute)');
      }
      
      return true;
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في فحص Rate Limiting: $e');
      }
      return false;
    }
  }
  
  /// تسجيل محاولة فاشلة
  static void recordFailedAttempt(String identifier) {
    try {
      _failedAttempts[identifier] = (_failedAttempts[identifier] ?? 0) + 1;
      
      if (kDebugMode) {
        print('⚠️ محاولة فاشلة مسجلة: $identifier (${_failedAttempts[identifier]}/$_maxFailedAttempts)');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في تسجيل المحاولة الفاشلة: $e');
      }
    }
  }
  
  /// فحص إذا كان المستخدم محظور
  static bool isUserLocked(String identifier) {
    try {
      final attempts = _failedAttempts[identifier] ?? 0;
      return attempts >= _maxFailedAttempts;
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في فحص حظر المستخدم: $e');
      }
      return false;
    }
  }
  
  /// إعادة تعيين محاولات المستخدم
  static void resetFailedAttempts(String identifier) {
    try {
      _failedAttempts.remove(identifier);
      
      if (kDebugMode) {
        print('🔄 تم إعادة تعيين محاولات المستخدم: $identifier');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في إعادة تعيين المحاولات: $e');
      }
    }
  }
  
  /// التحقق من صحة البيانات المدخلة
  static ValidationResult validateInput({
    String? email,
    String? password,
    String? phone,
    String? name,
    Map<String, dynamic>? customData,
  }) {
    try {
      final errors = <String>[];
      
      // التحقق من البريد الإلكتروني
      if (email != null) {
        if (email.isEmpty) {
          errors.add('البريد الإلكتروني مطلوب');
        } else if (!_isValidEmail(email)) {
          errors.add('تنسيق البريد الإلكتروني غير صحيح');
        }
      }
      
      // التحقق من كلمة المرور
      if (password != null) {
        if (password.isEmpty) {
          errors.add('كلمة المرور مطلوبة');
        } else if (password.length < 8) {
          errors.add('كلمة المرور يجب أن تكون 8 أحرف على الأقل');
        }
      }
      
      // التحقق من رقم الهاتف
      if (phone != null) {
        if (phone.isEmpty) {
          errors.add('رقم الهاتف مطلوب');
        } else if (!_isValidPhone(phone)) {
          errors.add('تنسيق رقم الهاتف غير صحيح');
        }
      }
      
      // التحقق من الاسم
      if (name != null) {
        if (name.isEmpty) {
          errors.add('الاسم مطلوب');
        } else if (name.length < 2) {
          errors.add('الاسم يجب أن يكون حرفين على الأقل');
        } else if (!_isValidName(name)) {
          errors.add('الاسم يحتوي على أحرف غير صالحة');
        }
      }
      
      // التحقق من البيانات المخصصة
      if (customData != null) {
        final customErrors = _validateCustomData(customData);
        errors.addAll(customErrors);
      }
      
      return ValidationResult(
        isValid: errors.isEmpty,
        errors: errors,
      );
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في التحقق من البيانات: $e');
      }
      return ValidationResult(
        isValid: false,
        errors: ['حدث خطأ في التحقق من البيانات'],
      );
    }
  }
  
  /// التحقق من صحة البريد الإلكتروني
  static bool _isValidEmail(String email) {
    final emailRegex = RegExp(
      r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$',
    );
    return emailRegex.hasMatch(email);
  }
  
  /// التحقق من صحة رقم الهاتف
  static bool _isValidPhone(String phone) {
    // إزالة المسافات والرموز
    final cleanPhone = phone.replaceAll(RegExp(r'[\s\-\(\)]'), '');
    
    // فحص الأرقام العراقية والسعودية
    final phoneRegex = RegExp(r'^\+?(964|966|1|44|33|49)[0-9]{8,12}$');
    return phoneRegex.hasMatch(cleanPhone);
  }
  
  /// التحقق من صحة الاسم
  static bool _isValidName(String name) {
    // السماح بالأحرف العربية والإنجليزية والمسافات
    final nameRegex = RegExp(r'^[\u0600-\u06FFa-zA-Z\s]+$');
    return nameRegex.hasMatch(name);
  }
  
  /// التحقق من البيانات المخصصة
  static List<String> _validateCustomData(Map<String, dynamic> data) {
    final errors = <String>[];
    
    try {
      // فحص حجم البيانات
      final jsonString = jsonEncode(data);
      if (jsonString.length > 10000) { // 10KB حد أقصى
        errors.add('حجم البيانات كبير جداً');
      }
      
      // فحص المفاتيح المحظورة
      final forbiddenKeys = ['password', 'token', 'secret', 'key'];
      for (final key in data.keys) {
        if (forbiddenKeys.any((forbidden) => key.toLowerCase().contains(forbidden))) {
          errors.add('مفتاح محظور في البيانات: $key');
        }
      }
      
      // فحص القيم الخطيرة
      for (final value in data.values) {
        if (value is String) {
          if (_containsSqlInjection(value)) {
            errors.add('محتوى خطير تم اكتشافه');
          }
          if (_containsXss(value)) {
            errors.add('محتوى XSS تم اكتشافه');
          }
        }
      }
    } catch (e) {
      errors.add('خطأ في فحص البيانات المخصصة');
    }
    
    return errors;
  }
  
  /// فحص SQL Injection
  static bool _containsSqlInjection(String input) {
    final sqlPatterns = [
      r'(\bSELECT\b|\bINSERT\b|\bUPDATE\b|\bDELETE\b|\bDROP\b|\bCREATE\b)',
      r'(\bUNION\b|\bOR\b|\bAND\b).*(\b1=1\b|\b1=0\b)',
      r'[\x27\x22;].*(\bOR\b|\bAND\b)',
      r'--.*',
      r'/\*.*\*/',
    ];
    
    for (final pattern in sqlPatterns) {
      if (RegExp(pattern, caseSensitive: false).hasMatch(input)) {
        return true;
      }
    }
    
    return false;
  }
  
  /// فحص XSS
  static bool _containsXss(String input) {
    final xssPatterns = [
      r'<script.*?>.*?</script>',
      r'javascript:',
      r'on\w+\s*=',
      r'<iframe.*?>',
      r'<object.*?>',
      r'<embed.*?>',
    ];
    
    for (final pattern in xssPatterns) {
      if (RegExp(pattern, caseSensitive: false).hasMatch(input)) {
        return true;
      }
    }
    
    return false;
  }
  
  /// تنظيف البيانات المدخلة
  static String sanitizeInput(String input) {
    try {
      // إزالة الأحرف الخطيرة
      String sanitized = input
          .replaceAll(RegExp(r'[<>"\'']'), '')
          .replaceAll(RegExp(r'javascript:', caseSensitive: false), '')
          .replaceAll(RegExp(r'on\w+\s*=', caseSensitive: false), '')
          .trim();
      
      // تحديد الطول الأقصى
      if (sanitized.length > 1000) {
        sanitized = sanitized.substring(0, 1000);
      }
      
      return sanitized;
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في تنظيف البيانات: $e');
      }
      return '';
    }
  }
  
  /// التحقق من صحة JWT Token
  static Future<bool> validateAuthToken(String? token) async {
    try {
      if (token == null || token.isEmpty) {
        if (kDebugMode) {
          print('⚠️ لا يوجد token للتحقق منه');
        }
        return false;
      }
      
      // التحقق من صحة Token
      final isValid = JWTService.verifyToken(token);
      
      if (!isValid) {
        if (kDebugMode) {
          print('❌ Token غير صحيح');
        }
        return false;
      }
      
      // فحص انتهاء الصلاحية
      if (JWTService.isTokenExpired(token)) {
        if (kDebugMode) {
          print('⚠️ Token منتهي الصلاحية');
        }
        
        // محاولة تجديد Token
        final refreshed = await SecureAuthService.refreshAccessToken();
        return refreshed;
      }
      
      return true;
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في التحقق من Token: $e');
      }
      return false;
    }
  }
  
  /// فحص الأذونات للعملية
  static bool checkPermission(String permission) {
    try {
      final token = SecureAuthService.accessToken;
      if (token == null) {
        if (kDebugMode) {
          print('⚠️ لا يوجد token للتحقق من الأذونات');
        }
        return false;
      }
      
      return JWTService.hasPermission(token, permission);
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في فحص الأذونات: $e');
      }
      return false;
    }
  }
  
  /// تسجيل محاولة وصول
  static Future<void> logAccessAttempt({
    required String endpoint,
    required String method,
    required bool success,
    String? userId,
    String? errorMessage,
  }) async {
    try {
      final logEntry = {
        'timestamp': DateTime.now().toIso8601String(),
        'endpoint': endpoint,
        'method': method,
        'success': success,
        'userId': userId,
        'errorMessage': errorMessage,
        'userAgent': 'VisionLens Mobile App',
      };
      
      // حفظ السجل محلياً (في التطبيق الحقيقي يُرسل للخادم)
      final prefs = await SharedPreferences.getInstance();
      final existingLogs = prefs.getStringList('api_access_logs') ?? [];
      
      existingLogs.add(jsonEncode(logEntry));
      
      // الاحتفاظ بآخر 1000 سجل فقط
      if (existingLogs.length > 1000) {
        existingLogs.removeRange(0, existingLogs.length - 1000);
      }
      
      await prefs.setStringList('api_access_logs', existingLogs);
      
      if (kDebugMode) {
        print('📝 تم تسجيل محاولة الوصول: $endpoint');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في تسجيل محاولة الوصول: $e');
      }
    }
  }
  
  /// الحصول على سجلات الوصول
  static Future<List<Map<String, dynamic>>> getAccessLogs({int? limit}) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final logsStrings = prefs.getStringList('api_access_logs') ?? [];
      
      final logs = logsStrings
          .map((logString) => jsonDecode(logString) as Map<String, dynamic>)
          .toList();
      
      // ترتيب حسب التاريخ (الأحدث أولاً)
      logs.sort((a, b) => b['timestamp'].compareTo(a['timestamp']));
      
      // تحديد العدد إذا طُلب
      if (limit != null && logs.length > limit) {
        return logs.take(limit).toList();
      }
      
      return logs;
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في جلب سجلات الوصول: $e');
      }
      return [];
    }
  }
  
  /// مسح سجلات الوصول القديمة
  static Future<void> clearOldLogs({Duration? olderThan}) async {
    try {
      final cutoffDate = DateTime.now().subtract(olderThan ?? const Duration(days: 30));
      final logs = await getAccessLogs();
      
      final filteredLogs = logs.where((log) {
        final logDate = DateTime.parse(log['timestamp']);
        return logDate.isAfter(cutoffDate);
      }).toList();
      
      final prefs = await SharedPreferences.getInstance();
      final logsStrings = filteredLogs.map((log) => jsonEncode(log)).toList();
      await prefs.setStringList('api_access_logs', logsStrings);
      
      if (kDebugMode) {
        print('🧹 تم مسح ${logs.length - filteredLogs.length} سجل قديم');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في مسح السجلات القديمة: $e');
      }
    }
  }
  
  /// فحص أمان الشبكة
  static Future<bool> checkNetworkSecurity() async {
    try {
      // فحص الاتصال الآمن
      final isSecure = await _checkSSLConnection();
      
      if (!isSecure) {
        if (kDebugMode) {
          print('⚠️ الاتصال غير آمن');
        }
        return false;
      }
      
      return true;
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في فحص أمان الشبكة: $e');
      }
      return false;
    }
  }
  
  /// فحص اتصال SSL
  static Future<bool> _checkSSLConnection() async {
    try {
      // في التطبيق الحقيقي، يجب فحص شهادة SSL
      // هنا سنعتبر الاتصال آمن إذا كان HTTPS
      return true; // مؤقت
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في فحص SSL: $e');
      }
      return false;
    }
  }
  
  /// إحصائيات الأمان
  static Future<Map<String, dynamic>> getSecurityStats() async {
    try {
      final logs = await getAccessLogs(limit: 100);
      final now = DateTime.now();
      
      // حساب الإحصائيات
      final successfulRequests = logs.where((log) => log['success'] == true).length;
      final failedRequests = logs.where((log) => log['success'] == false).length;
      
      final recentLogs = logs.where((log) {
        final logDate = DateTime.parse(log['timestamp']);
        return now.difference(logDate).inHours < 24;
      }).toList();
      
      return {
        'totalRequests': logs.length,
        'successfulRequests': successfulRequests,
        'failedRequests': failedRequests,
        'successRate': logs.isNotEmpty ? (successfulRequests / logs.length * 100).toStringAsFixed(1) : '0.0',
        'requestsLast24h': recentLogs.length,
        'lockedUsers': _failedAttempts.length,
        'activeRateLimits': _requestHistory.length,
        'lastUpdated': now.toIso8601String(),
      };
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في جلب إحصائيات الأمان: $e');
      }
      return {};
    }
  }

  /// فحص صحة البريد الإلكتروني
  static bool isValidEmail(String email) {
    try {
      final emailRegex = RegExp(
        r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$',
      );
      return emailRegex.hasMatch(email.trim());
    } catch (e) {
      return false;
    }
  }
}

/// نتيجة التحقق من البيانات
class ValidationResult {
  final bool isValid;
  final List<String> errors;
  
  const ValidationResult({
    required this.isValid,
    required this.errors,
  });
  
  String get errorMessage => errors.join('\n');
}
