import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import '../../app_properties.dart';
import '../../services/app_state.dart';
import '../../services/chat_service.dart';
import '../../models/chat_message.dart';

class ChatSupportPage extends StatefulWidget {
  const ChatSupportPage({super.key});

  @override
  State<ChatSupportPage> createState() => _ChatSupportPageState();
}

class _ChatSupportPageState extends State<ChatSupportPage> {
  final TextEditingController _messageController = TextEditingController();
  final ScrollController _scrollController = ScrollController();
  final AppState _appState = AppState();

  String? _conversationId;
  Stream<List<ChatMessage>>? _messagesStream;
  bool _isLoading = true;
  bool _isSending = false;

  @override
  void initState() {
    super.initState();
    _initializeChat();
  }

  Future<void> _initializeChat() async {
    try {
      final currentUser = _appState.currentUser;
      if (currentUser == null) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('يرجى تسجيل الدخول أولاً'),
              backgroundColor: AppColors.errorColor,
            ),
          );
          Navigator.pop(context);
        }
        return;
      }

      if (kDebugMode) {
        print('🔄 تهيئة الدردشة للعميل: ${currentUser.email}');
      }

      // الحصول على أو إنشاء محادثة
      final conversationId = await ChatService.getOrCreateConversation(currentUser);

      setState(() {
        _conversationId = conversationId;
        _messagesStream = ChatService.getMessages(conversationId);
        _isLoading = false;
      });

      // تحديد الرسائل كمقروءة
      await ChatService.markMessagesAsRead(conversationId, currentUser.id);

      if (kDebugMode) {
        print('✅ تم تهيئة الدردشة بنجاح');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في تهيئة الدردشة: $e');
      }

      if (mounted) {
        setState(() {
          _isLoading = false;
        });

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في تحميل الدردشة: $e'),
            backgroundColor: AppColors.errorColor,
          ),
        );
      }
    }
  }

  Future<void> _sendMessage() async {
    if (_messageController.text.trim().isEmpty || _isSending || _conversationId == null) return;

    final currentUser = _appState.currentUser;
    if (currentUser == null) return;

    final messageText = _messageController.text.trim();
    _messageController.clear();

    setState(() {
      _isSending = true;
    });

    try {
      await ChatService.sendMessage(
        conversationId: _conversationId!,
        senderId: currentUser.id,
        senderName: '${currentUser.firstName} ${currentUser.lastName}',
        senderType: 'customer',
        message: messageText,
      );

      _scrollToBottom();

      if (kDebugMode) {
        print('✅ تم إرسال الرسالة بنجاح');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في إرسال الرسالة: $e');
      }

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('فشل في إرسال الرسالة: $e'),
            backgroundColor: AppColors.errorColor,
          ),
        );

        // إعادة النص إلى الحقل
        _messageController.text = messageText;
      }
    } finally {
      if (mounted) {
        setState(() {
          _isSending = false;
        });
      }
    }
  }



  void _scrollToBottom() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_scrollController.hasClients) {
        _scrollController.animateTo(
          _scrollController.position.maxScrollExtent,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeOut,
        );
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.backgroundColor,
      appBar: AppBar(
        backgroundColor: AppColors.primaryColor,
        foregroundColor: AppColors.white,
        title: const Row(
          children: [
            Icon(Icons.support_agent, size: 24),
            SizedBox(width: 8),
            Text('الدردشة مع الدعم'),
          ],
        ),
        elevation: 0,
      ),
      body: _isLoading
          ? const Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  CircularProgressIndicator(),
                  SizedBox(height: 16),
                  Text('جاري تحميل الدردشة...'),
                ],
              ),
            )
          : Column(
              children: [
                // منطقة الرسائل
                Expanded(
                  child: _messagesStream == null
                      ? const Center(child: Text('خطأ في تحميل الرسائل'))
                      : StreamBuilder<List<ChatMessage>>(
                          stream: _messagesStream,
                          builder: (context, snapshot) {
                            if (snapshot.hasError) {
                              return Center(
                                child: Column(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    const Icon(
                                      Icons.error_outline,
                                      size: 64,
                                      color: AppColors.errorColor,
                                    ),
                                    const SizedBox(height: 16),
                                    Text(
                                      'خطأ في تحميل الرسائل',
                                      style: TextStyle(
                                        fontSize: 16,
                                        color: AppColors.textColor.withValues(alpha: 0.7),
                                      ),
                                    ),
                                    const SizedBox(height: 8),
                                    ElevatedButton(
                                      onPressed: _initializeChat,
                                      child: const Text('إعادة المحاولة'),
                                    ),
                                  ],
                                ),
                              );
                            }

                            if (!snapshot.hasData) {
                              return const Center(child: CircularProgressIndicator());
                            }

                            final messages = snapshot.data!;

                            if (messages.isEmpty) {
                              return Center(
                                child: Column(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    Icon(
                                      Icons.chat_bubble_outline,
                                      size: 64,
                                      color: AppColors.textColor.withValues(alpha: 0.3),
                                    ),
                                    const SizedBox(height: 16),
                                    Text(
                                      'ابدأ محادثة جديدة',
                                      style: TextStyle(
                                        fontSize: 16,
                                        color: AppColors.textColor.withValues(alpha: 0.7),
                                      ),
                                    ),
                                    const SizedBox(height: 8),
                                    Text(
                                      'اكتب رسالتك أدناه وسيقوم فريق الدعم بالرد عليك',
                                      textAlign: TextAlign.center,
                                      style: TextStyle(
                                        fontSize: 14,
                                        color: AppColors.textColor.withValues(alpha: 0.5),
                                      ),
                                    ),
                                  ],
                                ),
                              );
                            }

                            // التمرير التلقائي للأسفل عند وصول رسائل جديدة
                            WidgetsBinding.instance.addPostFrameCallback((_) {
                              _scrollToBottom();
                            });

                            return ListView.builder(
                              controller: _scrollController,
                              padding: const EdgeInsets.all(16),
                              itemCount: messages.length,
                              itemBuilder: (context, index) {
                                return _buildMessageBubble(messages[index]);
                              },
                            );
                          },
                        ),
                ),

                // شريط الإدخال
                _buildInputArea(),
              ],
            ),
    );
  }

  Widget _buildMessageBubble(ChatMessage message) {
    final isFromCustomer = message.isFromCustomer;

    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: isFromCustomer
            ? MainAxisAlignment.end
            : MainAxisAlignment.start,
        children: [
          if (!isFromCustomer) ...[
            CircleAvatar(
              radius: 16,
              backgroundColor: AppColors.primaryColor,
              child: const Icon(
                Icons.support_agent,
                size: 16,
                color: AppColors.white,
              ),
            ),
            const SizedBox(width: 8),
          ],

          Flexible(
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              decoration: BoxDecoration(
                color: isFromCustomer
                    ? AppColors.primaryColor
                    : AppColors.white,
                borderRadius: BorderRadius.circular(20),
                boxShadow: [
                  BoxShadow(
                    color: AppColors.black.withValues(alpha: 0.1),
                    blurRadius: 4,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // اسم المرسل (للإدارة فقط)
                  if (!isFromCustomer) ...[
                    Text(
                      message.senderName,
                      style: TextStyle(
                        color: AppColors.primaryColor,
                        fontSize: 12,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    const SizedBox(height: 4),
                  ],

                  // نص الرسالة
                  Text(
                    message.message,
                    style: TextStyle(
                      color: isFromCustomer
                          ? AppColors.white
                          : AppColors.textColor,
                      fontSize: 14,
                    ),
                  ),

                  const SizedBox(height: 4),

                  // الوقت وحالة القراءة
                  Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Text(
                        _formatTime(message.timestamp),
                        style: TextStyle(
                          color: isFromCustomer
                              ? AppColors.white.withValues(alpha: 0.7)
                              : AppColors.textColor.withValues(alpha: 0.6),
                          fontSize: 10,
                        ),
                      ),
                      if (isFromCustomer) ...[
                        const SizedBox(width: 4),
                        Icon(
                          message.isRead ? Icons.done_all : Icons.done,
                          size: 12,
                          color: message.isRead
                              ? AppColors.success
                              : AppColors.white.withValues(alpha: 0.7),
                        ),
                      ],
                    ],
                  ),
                ],
              ),
            ),
          ),

          if (isFromCustomer) ...[
            const SizedBox(width: 8),
            CircleAvatar(
              radius: 16,
              backgroundColor: AppColors.accentColor,
              child: Text(
                message.senderName.isNotEmpty
                    ? message.senderName[0].toUpperCase()
                    : 'أ',
                style: const TextStyle(
                  color: AppColors.white,
                  fontSize: 12,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }



  Widget _buildInputArea() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.white,
        boxShadow: [
          BoxShadow(
            color: AppColors.black.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: Row(
        children: [
          Expanded(
            child: TextField(
              controller: _messageController,
              decoration: InputDecoration(
                hintText: 'اكتب رسالتك هنا...',
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(25),
                  borderSide: BorderSide.none,
                ),
                filled: true,
                fillColor: AppColors.backgroundColor,
                contentPadding: const EdgeInsets.symmetric(
                  horizontal: 20,
                  vertical: 12,
                ),
              ),
              maxLines: null,
              textInputAction: TextInputAction.send,
              onSubmitted: (_) => _sendMessage(),
            ),
          ),
          const SizedBox(width: 8),
          FloatingActionButton(
            onPressed: _isSending ? null : _sendMessage,
            backgroundColor: _isSending
                ? AppColors.grey
                : AppColors.primaryColor,
            mini: true,
            child: _isSending
                ? const SizedBox(
                    width: 16,
                    height: 16,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(AppColors.white),
                    ),
                  )
                : const Icon(
                    Icons.send,
                    color: AppColors.white,
                  ),
          ),
        ],
      ),
    );
  }

  String _formatTime(DateTime time) {
    final now = DateTime.now();
    final difference = now.difference(time);
    
    if (difference.inMinutes < 1) {
      return 'الآن';
    } else if (difference.inHours < 1) {
      return 'منذ ${difference.inMinutes} دقيقة';
    } else if (difference.inDays < 1) {
      return 'منذ ${difference.inHours} ساعة';
    } else {
      return '${time.day}/${time.month}';
    }
  }

  @override
  void dispose() {
    _messageController.dispose();
    _scrollController.dispose();
    super.dispose();
  }
}


