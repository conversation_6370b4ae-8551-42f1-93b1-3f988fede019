import 'package:flutter/material.dart';
import '../../app_properties.dart';

class FAQPage extends StatefulWidget {
  const FAQPage({super.key});

  @override
  State<FAQPage> createState() => _FAQPageState();
}

class _FAQPageState extends State<FAQPage> {
  final TextEditingController _searchController = TextEditingController();
  List<FAQItem> _allFAQs = [];
  List<FAQItem> _filteredFAQs = [];
  String _selectedCategory = 'الكل';

  final List<String> _categories = [
    'الكل',
    'الطلبات',
    'الدفع',
    'الشحن',
    'المنتجات',
    'الحساب',
    'أخرى',
  ];

  @override
  void initState() {
    super.initState();
    _loadFAQs();
    _searchController.addListener(_filterFAQs);
  }

  void _loadFAQs() {
    _allFAQs = [
      // أسئلة الطلبات
      FAQItem(
        question: 'كيف يمكنني تتبع طلبي؟',
        answer: 'يمكنك تتبع طلبك من خلال الذهاب إلى "طلباتي" في الملف الشخصي، ثم النقر على الطلب المطلوب. ستجد معلومات مفصلة عن حالة الطلب ومراحل الشحن.',
        category: 'الطلبات',
      ),
      FAQItem(
        question: 'كم يستغرق تجهيز الطلب؟',
        answer: 'عادة ما يستغرق تجهيز الطلب من 1-2 يوم عمل. في حالة النظارات الطبية، قد يستغرق الأمر 3-5 أيام عمل حسب نوع العدسات المطلوبة.',
        category: 'الطلبات',
      ),
      FAQItem(
        question: 'هل يمكنني إلغاء أو تعديل طلبي؟',
        answer: 'يمكنك إلغاء أو تعديل طلبك خلال 30 دقيقة من تأكيد الطلب. بعد ذلك، يرجى التواصل مع خدمة العملاء لمساعدتك.',
        category: 'الطلبات',
      ),

      // أسئلة الدفع
      FAQItem(
        question: 'ما هي طرق الدفع المتاحة؟',
        answer: 'نقبل جميع البطاقات الائتمانية (فيزا، ماستركارد، أمريكان إكسبريس)، المحافظ الرقمية (Apple Pay، Google Pay)، والدفع عند الاستلام في المناطق المتاحة.',
        category: 'الدفع',
      ),
      FAQItem(
        question: 'هل الدفع آمن؟',
        answer: 'نعم، جميع المعاملات محمية بتشفير SSL 256-bit ونتبع أعلى معايير الأمان المصرفي. لا نحتفظ ببيانات البطاقات الائتمانية على خوادمنا.',
        category: 'الدفع',
      ),
      FAQItem(
        question: 'متى سيتم خصم المبلغ من حسابي؟',
        answer: 'يتم خصم المبلغ فور تأكيد الطلب. في حالة الإلغاء، سيتم إرجاع المبلغ خلال 3-7 أيام عمل حسب البنك.',
        category: 'الدفع',
      ),

      // أسئلة الشحن
      FAQItem(
        question: 'كم تكلفة الشحن؟',
        answer: 'الشحن مجاني للطلبات أكثر من 200 ريال. للطلبات الأقل، تكلفة الشحن 25 ريال داخل المدن الرئيسية و 35 ريال للمناطق الأخرى.',
        category: 'الشحن',
      ),
      FAQItem(
        question: 'كم يستغرق وصول الطلب؟',
        answer: 'التوصيل خلال 2-3 أيام عمل في المدن الرئيسية، و 4-7 أيام في المناطق الأخرى. للطلبات العاجلة، يتوفر التوصيل السريع خلال 24 ساعة.',
        category: 'الشحن',
      ),
      FAQItem(
        question: 'هل يمكنني تغيير عنوان التوصيل؟',
        answer: 'يمكنك تغيير عنوان التوصيل قبل شحن الطلب من خلال التواصل مع خدمة العملاء أو من خلال صفحة تتبع الطلب.',
        category: 'الشحن',
      ),

      // أسئلة المنتجات
      FAQItem(
        question: 'كيف أختار المقاس المناسب للنظارة؟',
        answer: 'يمكنك استخدام دليل المقاسات المتوفر في صفحة كل منتج. كما يمكنك تجربة النظارة افتراضياً باستخدام الكاميرا أو زيارة أحد فروعنا.',
        category: 'المنتجات',
      ),
      FAQItem(
        question: 'هل تتوفر عدسات طبية؟',
        answer: 'نعم، نوفر جميع أنواع العدسات الطبية بما في ذلك العدسات أحادية البؤرة، متعددة البؤر، والعدسات المضادة للانعكاس.',
        category: 'المنتجات',
      ),
      FAQItem(
        question: 'ما هي سياسة الإرجاع والاستبدال؟',
        answer: 'يمكنك إرجاع أو استبدال المنتج خلال 14 يوم من تاريخ الاستلام، بشرط أن يكون في حالته الأصلية. النظارات الطبية غير قابلة للإرجاع إلا في حالة عيب التصنيع.',
        category: 'المنتجات',
      ),

      // أسئلة الحساب
      FAQItem(
        question: 'كيف أنشئ حساب جديد؟',
        answer: 'يمكنك إنشاء حساب جديد من خلال النقر على "إنشاء حساب" في الصفحة الرئيسية، ثم إدخال بياناتك الأساسية وتأكيد البريد الإلكتروني.',
        category: 'الحساب',
      ),
      FAQItem(
        question: 'نسيت كلمة المرور، ماذا أفعل؟',
        answer: 'انقر على "نسيت كلمة المرور" في صفحة تسجيل الدخول، ثم أدخل بريدك الإلكتروني. ستصلك رسالة لإعادة تعيين كلمة المرور.',
        category: 'الحساب',
      ),
      FAQItem(
        question: 'كيف أحدث بياناتي الشخصية؟',
        answer: 'يمكنك تحديث بياناتك من خلال الذهاب إلى الملف الشخصي، ثم النقر على "تعديل الملف الشخصي" وإجراء التغييرات المطلوبة.',
        category: 'الحساب',
      ),

      // أسئلة أخرى
      FAQItem(
        question: 'هل لديكم فروع فعلية؟',
        answer: 'نعم، لدينا عدة فروع في المدن الرئيسية. يمكنك العثور على أقرب فرع من خلال قسم "المتاجر" في التطبيق.',
        category: 'أخرى',
      ),
      FAQItem(
        question: 'كيف يمكنني التواصل مع خدمة العملاء؟',
        answer: 'يمكنك التواصل معنا من خلال الدردشة المباشرة في التطبيق، البريد الإلكتروني، أو الهاتف. أوقات العمل من 9 صباحاً حتى 9 مساءً.',
        category: 'أخرى',
      ),
    ];

    _filteredFAQs = _allFAQs;
    setState(() {});
  }

  void _filterFAQs() {
    final query = _searchController.text.toLowerCase();
    
    setState(() {
      _filteredFAQs = _allFAQs.where((faq) {
        final matchesSearch = query.isEmpty ||
            faq.question.toLowerCase().contains(query) ||
            faq.answer.toLowerCase().contains(query);
        
        final matchesCategory = _selectedCategory == 'الكل' ||
            faq.category == _selectedCategory;
        
        return matchesSearch && matchesCategory;
      }).toList();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.backgroundColor,
      appBar: AppBar(
        backgroundColor: AppColors.primaryColor,
        foregroundColor: AppColors.white,
        title: const Row(
          children: [
            Icon(Icons.help_outline, size: 24),
            SizedBox(width: 8),
            Text('الأسئلة الشائعة'),
          ],
        ),
        elevation: 0,
      ),
      body: Column(
        children: [
          // شريط البحث والفلترة
          Container(
            padding: const EdgeInsets.all(16),
            color: AppColors.white,
            child: Column(
              children: [
                // شريط البحث
                TextField(
                  controller: _searchController,
                  decoration: InputDecoration(
                    hintText: 'ابحث في الأسئلة الشائعة...',
                    prefixIcon: const Icon(Icons.search),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: BorderSide.none,
                    ),
                    filled: true,
                    fillColor: AppColors.backgroundColor,
                  ),
                ),
                
                const SizedBox(height: 12),
                
                // فلتر الفئات
                SizedBox(
                  height: 40,
                  child: ListView.builder(
                    scrollDirection: Axis.horizontal,
                    itemCount: _categories.length,
                    itemBuilder: (context, index) {
                      final category = _categories[index];
                      final isSelected = category == _selectedCategory;
                      
                      return Padding(
                        padding: const EdgeInsets.only(right: 8),
                        child: FilterChip(
                          label: Text(category),
                          selected: isSelected,
                          onSelected: (selected) {
                            setState(() {
                              _selectedCategory = category;
                            });
                            _filterFAQs();
                          },
                          backgroundColor: AppColors.backgroundColor,
                          selectedColor: AppColors.primaryColor,
                          labelStyle: TextStyle(
                            color: isSelected ? AppColors.white : AppColors.textColor,
                          ),
                        ),
                      );
                    },
                  ),
                ),
              ],
            ),
          ),
          
          // قائمة الأسئلة
          Expanded(
            child: _filteredFAQs.isEmpty
                ? _buildEmptyState()
                : ListView.builder(
                    padding: const EdgeInsets.all(16),
                    itemCount: _filteredFAQs.length,
                    itemBuilder: (context, index) {
                      return _buildFAQItem(_filteredFAQs[index]);
                    },
                  ),
          ),
        ],
      ),
    );
  }

  Widget _buildFAQItem(FAQItem faq) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: ExpansionTile(
        title: Text(
          faq.question,
          style: const TextStyle(
            fontWeight: FontWeight.w600,
            fontSize: 16,
          ),
        ),
        leading: Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: AppColors.primaryColor.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(
            _getCategoryIcon(faq.category),
            color: AppColors.primaryColor,
            size: 20,
          ),
        ),
        children: [
          Padding(
            padding: const EdgeInsets.fromLTRB(16, 0, 16, 16),
            child: Text(
              faq.answer,
              style: TextStyle(
                color: AppColors.textColor.withValues(alpha: 0.8),
                fontSize: 14,
                height: 1.5,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.search_off,
            size: 64,
            color: AppColors.textColor.withValues(alpha: 0.3),
          ),
          const SizedBox(height: 16),
          Text(
            'لم يتم العثور على نتائج',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: AppColors.textColor.withValues(alpha: 0.6),
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'جرب البحث بكلمات مختلفة أو اختر فئة أخرى',
            style: TextStyle(
              fontSize: 14,
              color: AppColors.textColor.withValues(alpha: 0.5),
            ),
          ),
        ],
      ),
    );
  }

  IconData _getCategoryIcon(String category) {
    switch (category) {
      case 'الطلبات':
        return Icons.shopping_bag_outlined;
      case 'الدفع':
        return Icons.payment;
      case 'الشحن':
        return Icons.local_shipping_outlined;
      case 'المنتجات':
        return Icons.inventory_2_outlined;
      case 'الحساب':
        return Icons.account_circle_outlined;
      default:
        return Icons.help_outline;
    }
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }
}

class FAQItem {
  final String question;
  final String answer;
  final String category;

  FAQItem({
    required this.question,
    required this.answer,
    required this.category,
  });
}
