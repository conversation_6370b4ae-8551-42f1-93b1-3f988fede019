import 'product.dart';

class CartItem {
  final String id;
  final Product product;
  int quantity;
  final String? selectedColor;
  final String? selectedSize;
  final String? selectedPower; // للعدسات
  final Map<String, dynamic>? customizations;
  final DateTime addedAt;

  CartItem({
    required this.id,
    required this.product,
    this.quantity = 1,
    this.selectedColor,
    this.selectedSize,
    this.selectedPower,
    this.customizations,
    DateTime? addedAt,
  }) : addedAt = addedAt ?? DateTime.now();

  double get totalPrice => product.price * quantity;
  double get originalTotalPrice => (product.originalPrice ?? product.price) * quantity;
  double get savings => originalTotalPrice - totalPrice;
  bool get hasDiscount => product.originalPrice != null && product.originalPrice! > product.price;

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'product': product.toJson(),
      'quantity': quantity,
      'selectedColor': selectedColor,
      'selectedSize': selectedSize,
      'selectedPower': selectedPower,
      'customizations': customizations,
      'addedAt': addedAt.toIso8601String(),
    };
  }

  factory CartItem.fromJson(Map<String, dynamic> json) {
    return CartItem(
      id: json['id'] ?? '',
      product: Product.fromJson(json['product']),
      quantity: json['quantity'] ?? 1,
      selectedColor: json['selectedColor'],
      selectedSize: json['selectedSize'],
      selectedPower: json['selectedPower'],
      customizations: json['customizations'],
      addedAt: DateTime.parse(json['addedAt'] ?? DateTime.now().toIso8601String()),
    );
  }

  CartItem copyWith({
    String? id,
    Product? product,
    int? quantity,
    String? selectedColor,
    String? selectedSize,
    String? selectedPower,
    Map<String, dynamic>? customizations,
    DateTime? addedAt,
  }) {
    return CartItem(
      id: id ?? this.id,
      product: product ?? this.product,
      quantity: quantity ?? this.quantity,
      selectedColor: selectedColor ?? this.selectedColor,
      selectedSize: selectedSize ?? this.selectedSize,
      selectedPower: selectedPower ?? this.selectedPower,
      customizations: customizations ?? this.customizations,
      addedAt: addedAt ?? this.addedAt,
    );
  }

  @override
  String toString() {
    return 'CartItem(id: $id, product: ${product.name}, quantity: $quantity)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is CartItem && 
           other.id == id && 
           other.product.id == product.id &&
           other.selectedColor == selectedColor &&
           other.selectedSize == selectedSize &&
           other.selectedPower == selectedPower;
  }

  @override
  int get hashCode => Object.hash(id, product.id, selectedColor, selectedSize, selectedPower);
}
