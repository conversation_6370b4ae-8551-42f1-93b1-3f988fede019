import 'package:flutter/foundation.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import '../models/product.dart';
import '../models/category.dart' as category_model;
import 'firebase_real_service.dart';
import 'firebase_platform_service.dart';

/// دالة مساعدة للطباعة في وضع التطوير فقط
void _debugLog(String message) {
  if (kDebugMode) {
    debugPrint('📊 Firestore Data: $message');
  }
}

/// خدمة البيانات باستخدام Firestore - مشاركة البيانات بين جميع المستخدمين
class FirestoreDataService {
  static FirebaseFirestore? get _firestore => FirebaseRealService.firestore;

  // ==================== إدارة المنتجات ====================

  /// جلب قائمة المنتجات من Firestore
  static Future<List<Product>> getProducts() async {
    try {
      if (kDebugMode) {
        debugPrint(
          '🔄 [${DateTime.now()}] محاولة جلب المنتجات من Firestore...',
        );
      }

      // استخدم Firebase Platform Service (يختار الخدمة المناسبة حسب المنصة)
      if (kDebugMode) {
        debugPrint(
          '🔄 [${DateTime.now()}] استخدام Firebase Platform Service...',
        );
      }
      final products = await FirebasePlatformService.getProducts();
      if (kDebugMode) {
        debugPrint(
          '✅ [${DateTime.now()}] تم استلام ${products.length} منتج من Firebase Platform Service',
        );

        for (int i = 0; i < products.length && i < 3; i++) {
          debugPrint(
            '📦 منتج ${i + 1}: ${products[i].name} (${products[i].id})',
          );
        }
      }

      return products;
    } catch (e) {
      if (kDebugMode) {
        _debugLog('❌ خطأ في جلب المنتجات من Firestore: $e');
        _debugLog('❌ نوع الخطأ: ${e.runtimeType}');
      }
      return [];
    }
  }

  /// إضافة منتج جديد إلى Firestore
  static Future<void> addProduct(Product product) async {
    try {
      if (kDebugMode) {
        _debugLog('🔄 محاولة إضافة منتج إلى Firestore: ${product.name}');
        _debugLog('🔄 معرف المنتج: ${product.id}');
      }

      // استخدم Firebase Platform Service
      _debugLog('🔄 استخدام Firebase Platform Service لإضافة المنتج...');
      final success = await FirebasePlatformService.addProduct(product);
      if (!success) {
        throw Exception('فشل في إضافة المنتج عبر Firebase Platform Service');
      }
      return;
    } catch (e) {
      if (kDebugMode) {
        _debugLog('❌ خطأ في إضافة المنتج إلى Firestore: $e');
        _debugLog('❌ نوع الخطأ: ${e.runtimeType}');
      }
      rethrow;
    }
  }

  /// تعديل منتج في Firestore
  static Future<void> updateProduct(Product product) async {
    try {
      if (kDebugMode) {
        _debugLog('🔄 محاولة تعديل منتج في Firestore: ${product.name}');
      }

      // استخدم Firebase Platform Service
      _debugLog('🔄 استخدام Firebase Platform Service لتعديل المنتج...');
      final success = await FirebasePlatformService.updateProduct(product);
      if (!success) {
        throw Exception('فشل في تعديل المنتج عبر Firebase Platform Service');
      }
      return;
    } catch (e) {
      if (kDebugMode) {
        _debugLog('❌ خطأ في تعديل المنتج في Firestore: $e');
      }
      rethrow;
    }
  }

  /// حذف منتج من Firestore
  static Future<void> deleteProduct(String productId) async {
    try {
      if (kDebugMode) {
        _debugLog('🔄 محاولة حذف منتج من Firestore: $productId');
      }

      // استخدم Firebase Platform Service
      _debugLog('🔄 استخدام Firebase Platform Service لحذف المنتج...');
      final success = await FirebasePlatformService.deleteProduct(productId);
      if (!success) {
        throw Exception('فشل في حذف المنتج عبر Firebase Platform Service');
      }
      return;
    } catch (e) {
      if (kDebugMode) {
        _debugLog('❌ خطأ في حذف المنتج من Firestore: $e');
      }
      rethrow;
    }
  }

  /// البحث في المنتجات
  static Future<List<Product>> searchProducts(String query) async {
    final products = await getProducts();

    if (query.isEmpty) {
      return products;
    }

    return products.where((product) {
      return product.name.toLowerCase().contains(query.toLowerCase()) ||
          product.description.toLowerCase().contains(query.toLowerCase()) ||
          (product.brand?.toLowerCase().contains(query.toLowerCase()) ?? false);
    }).toList();
  }

  /// جلب المنتجات حسب الفئة
  static Future<List<Product>> getProductsByCategory(String categoryId) async {
    try {
      if (_firestore == null) return [];

      final snapshot = await _firestore!
          .collection('products')
          .where('categoryId', isEqualTo: categoryId)
          .get();

      return snapshot.docs
          .map((doc) => Product.fromJson({...doc.data(), 'id': doc.id}))
          .toList();
    } catch (e) {
      if (kDebugMode) {
        _debugLog('❌ خطأ في جلب المنتجات حسب الفئة: $e');
      }
      return [];
    }
  }

  /// جلب المنتجات المميزة
  static Future<List<Product>> getFeaturedProducts() async {
    try {
      if (_firestore == null) return [];

      final snapshot = await _firestore!
          .collection('products')
          .where('isFeatured', isEqualTo: true)
          .get();

      return snapshot.docs
          .map((doc) => Product.fromJson({...doc.data(), 'id': doc.id}))
          .toList();
    } catch (e) {
      if (kDebugMode) {
        _debugLog('❌ خطأ في جلب المنتجات المميزة: $e');
      }
      return [];
    }
  }

  // ==================== إدارة الفئات ====================

  /// جلب قائمة الفئات من Firestore
  static Future<List<category_model.Category>> getCategories() async {
    try {
      // استخدم Firebase Platform Service
      _debugLog('🔄 استخدام Firebase Platform Service لجلب الفئات...');
      final categoriesData = await FirebasePlatformService.getCategories();

      final categories = categoriesData
          .map((data) => category_model.Category.fromJson(data))
          .toList();

      if (kDebugMode) {
        _debugLog(
          '📂 تم جلب ${categories.length} فئة من Firebase Platform Service',
        );
      }

      return categories;
    } catch (e) {
      if (kDebugMode) {
        _debugLog('❌ خطأ في جلب الفئات من Firestore: $e');
      }
      return [];
    }
  }

  /// إضافة فئة جديدة إلى Firestore
  static Future<void> addCategory(category_model.Category category) async {
    try {
      _debugLog('🔄 إضافة فئة: ${category.name}');

      // استخدام Firebase Platform Service مباشرة للجميع
      final success = await FirebasePlatformService.addCategory(category.toJson());
      if (!success) {
        throw Exception('فشل في إضافة الفئة عبر Firebase Platform Service');
      }

      _debugLog('✅ تم إضافة الفئة بنجاح: ${category.name}');

      if (kDebugMode) {
        _debugLog('✅ تم إضافة الفئة إلى Firestore: ${category.name}');
      }
    } catch (e) {
      if (kDebugMode) {
        _debugLog('❌ خطأ في إضافة الفئة إلى Firestore: $e');
      }
      rethrow;
    }
  }

  /// تحديث فئة في Firestore
  static Future<void> updateCategory(category_model.Category category) async {
    try {
      _debugLog('🔄 تعديل فئة: ${category.name}');

      // استخدام Firebase Platform Service مباشرة للجميع
      final success = await FirebasePlatformService.updateCategory(category.toJson());
      if (!success) {
        throw Exception('فشل في تعديل الفئة عبر Firebase Platform Service');
      }

      _debugLog('✅ تم تعديل الفئة بنجاح: ${category.name}');
    } catch (e) {
      if (kDebugMode) {
        _debugLog('❌ خطأ في تحديث الفئة في Firestore: $e');
      }
      rethrow;
    }
  }

  /// حذف فئة من Firestore
  static Future<void> deleteCategory(String categoryId) async {
    try {
      _debugLog('🔄 حذف فئة: $categoryId');

      // استخدام Firebase Platform Service مباشرة للجميع
      final success = await FirebasePlatformService.deleteCategory(categoryId);
      if (!success) {
        throw Exception('فشل في حذف الفئة عبر Firebase Platform Service');
      }

      _debugLog('✅ تم حذف الفئة بنجاح: $categoryId');
    } catch (e) {
      if (kDebugMode) {
        _debugLog('❌ خطأ في حذف الفئة من Firestore: $e');
      }
      rethrow;
    }
  }

  // ==================== مزامنة البيانات ====================

  /// مزامنة البيانات المحلية مع Firestore
  static Future<void> syncLocalDataToFirestore() async {
    try {
      if (_firestore == null) {
        if (kDebugMode) _debugLog('⚠️ Firestore غير متاح للمزامنة');
        return;
      }

      if (kDebugMode) {
        _debugLog('🔄 بدء مزامنة البيانات المحلية مع Firestore...');
      }

      // مزامنة المنتجات المحلية
      // يمكن إضافة هذا لاحقاً إذا احتجنا

      if (kDebugMode) {
        _debugLog('✅ تمت مزامنة البيانات مع Firestore');
      }
    } catch (e) {
      if (kDebugMode) {
        _debugLog('❌ خطأ في مزامنة البيانات: $e');
      }
    }
  }

  /// التحقق من اتصال Firestore
  static Future<bool> isFirestoreAvailable() async {
    try {
      if (_firestore == null) return false;
      await _firestore!.collection('test').limit(1).get();
      return true;
    } catch (e) {
      return false;
    }
  }

  /// === إدارة الطلبات ===

  /// جلب جميع الطلبات من Firestore
  static Future<List<Map<String, dynamic>>> getOrders() async {
    try {
      _debugLog('🔄 جلب الطلبات من Firestore...');

      // استخدم Firebase Platform Service دائماً (يختار الخدمة المناسبة تلقائياً)
      return await FirebasePlatformService.getOrders();
    } catch (e) {
      if (kDebugMode) {
        _debugLog('❌ خطأ في جلب الطلبات من Firestore: $e');
      }
      return [];
    }
  }

  /// تحديث حالة الطلب في Firestore
  static Future<bool> updateOrderStatus(String orderId, String status) async {
    try {
      _debugLog('🔄 تحديث حالة الطلب: $orderId إلى $status');

      // استخدام Firebase Platform Service للجميع
      final success = await FirebasePlatformService.updateOrderStatus(orderId, status);

      if (success) {
        _debugLog('✅ تم تحديث حالة الطلب بنجاح في Firebase');
      } else {
        _debugLog('❌ فشل في تحديث حالة الطلب في Firebase');
      }

      return success;
    } catch (e) {
      if (kDebugMode) {
        _debugLog('❌ خطأ في تحديث حالة الطلب: $e');
      }
      return false;
    }
  }

  /// جلب المستخدمين من Firestore
  static Future<List<Map<String, dynamic>>> getUsers() async {
    try {
      // للويب: استخدم Firebase Web Service
      if (kIsWeb) {
        _debugLog('🌐 استخدام Firebase Web Service لجلب المستخدمين...');
        // مؤقتاً نعيد قائمة فارغة للموبايل
        return [];
      }

      // للمنصات الأخرى: استخدم Firebase الحقيقي
      if (_firestore == null) {
        if (kDebugMode) {
          _debugLog('⚠️ Firestore غير متاح');
        }
        return [];
      }

      final snapshot = await _firestore!.collection('users').get();
      final users = snapshot.docs
          .map((doc) => {...doc.data(), 'id': doc.id})
          .toList();

      if (kDebugMode) {
        _debugLog('👥 تم جلب ${users.length} مستخدم من Firestore');
      }

      return users;
    } catch (e) {
      if (kDebugMode) {
        _debugLog('❌ خطأ في جلب المستخدمين من Firestore: $e');
      }
      return [];
    }
  }

  /// إضافة طلب جديد إلى Firestore
  static Future<void> addOrder(Map<String, dynamic> orderData) async {
    try {
      _debugLog('🔄 إضافة طلب جديد');

      // استخدام Firebase Platform Service مباشرة للجميع
      final success = await FirebasePlatformService.addOrder(orderData);
      if (!success) {
        throw Exception('فشل في إضافة الطلب عبر Firebase Platform Service');
      }

      _debugLog('✅ تم إضافة الطلب بنجاح');

      if (kDebugMode) {
        _debugLog('✅ تم إضافة الطلب إلى Firestore: ${orderData['id']}');
      }
    } catch (e) {
      if (kDebugMode) {
        _debugLog('❌ خطأ في إضافة الطلب إلى Firestore: $e');
      }
      rethrow;
    }
  }

  /// === إدارة البراندات ===

  /// جلب جميع البراندات من Firestore
  static Future<List<Map<String, dynamic>>> getBrands() async {
    try {
      // للويب: استخدم Firebase Web Service
      if (kIsWeb) {
        _debugLog('🌐 استخدام Firebase Web Service لجلب البراندات...');
        return await FirebasePlatformService.getBrands();
      }

      // للمنصات الأخرى: استخدم Firebase الحقيقي
      if (_firestore == null) {
        if (kDebugMode) {
          _debugLog('⚠️ Firestore غير متاح');
        }
        return [];
      }

      final snapshot = await _firestore!.collection('brands').get();
      final brands = snapshot.docs
          .map((doc) => {...doc.data(), 'id': doc.id})
          .toList();

      if (kDebugMode) {
        _debugLog('🏷️ تم جلب ${brands.length} براند من Firestore');
      }

      return brands;
    } catch (e) {
      if (kDebugMode) {
        _debugLog('❌ خطأ في جلب البراندات من Firestore: $e');
      }
      return [];
    }
  }

  /// إضافة براند جديد
  static Future<void> addBrand(Map<String, dynamic> brandData) async {
    try {
      // للويب: استخدم Firebase Web Service
      if (kIsWeb) {
        _debugLog('🌐 استخدام Firebase Web Service لإضافة البراند...');
        final success = await FirebasePlatformService.addBrand(brandData);
        if (!success) {
          throw Exception('فشل في إضافة البراند عبر Firebase Web Service');
        }
        return;
      }

      // للمنصات الأخرى: استخدم Firebase الحقيقي
      if (_firestore == null) {
        if (kDebugMode) {
          _debugLog('⚠️ Firestore غير متاح');
        }
        return;
      }

      // استخدام Firebase Platform Service للاتساق
      final success = await FirebasePlatformService.addBrand(brandData);
      if (!success) {
        throw Exception('فشل في إضافة البراند عبر Firebase Platform Service');
      }

      if (kDebugMode) {
        _debugLog('✅ تم إضافة البراند إلى Firestore: ${brandData['name']}');
      }
    } catch (e) {
      if (kDebugMode) {
        _debugLog('❌ خطأ في إضافة البراند إلى Firestore: $e');
      }
      rethrow;
    }
  }

  /// تحديث براند موجود
  static Future<void> updateBrand(Map<String, dynamic> brandData) async {
    try {
      // للويب: استخدم Firebase Web Service
      if (kIsWeb) {
        _debugLog('🌐 استخدام Firebase Web Service لتحديث البراند...');
        final success = await FirebasePlatformService.updateBrand(brandData);
        if (!success) {
          throw Exception('فشل في تحديث البراند عبر Firebase Web Service');
        }
        return;
      }

      // للمنصات الأخرى: استخدم Firebase الحقيقي
      if (_firestore == null) {
        if (kDebugMode) {
          _debugLog('⚠️ Firestore غير متاح');
        }
        return;
      }

      await _firestore!
          .collection('brands')
          .doc(brandData['id'])
          .update(brandData);

      if (kDebugMode) {
        _debugLog('✅ تم تحديث البراند في Firestore: ${brandData['name']}');
      }
    } catch (e) {
      if (kDebugMode) {
        _debugLog('❌ خطأ في تحديث البراند في Firestore: $e');
      }
      rethrow;
    }
  }

  /// حذف براند
  static Future<void> deleteBrand(String brandId) async {
    try {
      // للويب: استخدم Firebase Web Service
      if (kIsWeb) {
        _debugLog('🌐 استخدام Firebase Web Service لحذف البراند...');
        final success = await FirebasePlatformService.deleteBrand(brandId);
        if (!success) {
          throw Exception('فشل في حذف البراند عبر Firebase Web Service');
        }
        return;
      }

      // للمنصات الأخرى: استخدم Firebase الحقيقي
      if (_firestore == null) {
        if (kDebugMode) {
          _debugLog('⚠️ Firestore غير متاح');
        }
        return;
      }

      await _firestore!.collection('brands').doc(brandId).delete();

      if (kDebugMode) {
        _debugLog('✅ تم حذف البراند من Firestore: $brandId');
      }
    } catch (e) {
      if (kDebugMode) {
        _debugLog('❌ خطأ في حذف البراند من Firestore: $e');
      }
      rethrow;
    }
  }
}
