import 'product.dart';

class WishlistItem {
  final String id;
  final Product product;
  final DateTime addedAt;
  final String? notes;
  final bool isNotificationEnabled;

  const WishlistItem({
    required this.id,
    required this.product,
    required this.addedAt,
    this.notes,
    this.isNotificationEnabled = true,
  });

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'product': product.toJson(),
      'addedAt': addedAt.toIso8601String(),
      'notes': notes,
      'isNotificationEnabled': isNotificationEnabled,
    };
  }

  factory WishlistItem.fromJson(Map<String, dynamic> json) {
    return WishlistItem(
      id: json['id'] ?? '',
      product: Product.fromJson(json['product']),
      addedAt: DateTime.parse(json['addedAt'] ?? DateTime.now().toIso8601String()),
      notes: json['notes'],
      isNotificationEnabled: json['isNotificationEnabled'] ?? true,
    );
  }

  WishlistItem copyWith({
    String? id,
    Product? product,
    DateTime? addedAt,
    String? notes,
    bool? isNotificationEnabled,
  }) {
    return WishlistItem(
      id: id ?? this.id,
      product: product ?? this.product,
      addedAt: addedAt ?? this.addedAt,
      notes: notes ?? this.notes,
      isNotificationEnabled: isNotificationEnabled ?? this.isNotificationEnabled,
    );
  }

  @override
  String toString() {
    return 'WishlistItem(id: $id, product: ${product.name}, addedAt: $addedAt)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is WishlistItem && 
           other.id == id && 
           other.product.id == product.id;
  }

  @override
  int get hashCode => Object.hash(id, product.id);
}
