import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/cart_item.dart';
import '../models/wishlist_item.dart';
import 'app_state.dart';
import '../models/user_simple.dart' as user_model;
import '../models/product.dart';
import '../models/category.dart' as category_model;
import '../models/order.dart';
import '../models/payment_method.dart' as payment_model;

class StorageService {
  static const String _cartKey = 'cart_items';
  static const String _wishlistKey = 'wishlist_items';
  static const String _userKey = 'current_user';
  static const String _searchHistoryKey = 'search_history';
  static const String _adminEmailsKey = 'admin_emails';

  // حفظ السلة
  static Future<void> saveCart(List<CartItem> cartItems) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final cartJson = cartItems.map((item) => item.toJson()).toList();
      await prefs.setString(_cart<PERSON><PERSON>, j<PERSON><PERSON><PERSON><PERSON>(cartJson));
    } catch (e) {
      if (kDebugMode) {
        print('خطأ في حفظ السلة: $e');
      }
    }
  }

  // تحميل السلة
  static Future<List<CartItem>> loadCart() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final cartString = prefs.getString(_cartKey);
      if (cartString != null) {
        final cartJson = jsonDecode(cartString) as List;
        return cartJson.map((item) => CartItem.fromJson(item)).toList();
      }
    } catch (e) {
      if (kDebugMode) {
        print('خطأ في تحميل السلة: $e');
      }
    }
    return [];
  }

  // حفظ المفضلة
  static Future<void> saveWishlist(List<WishlistItem> wishlistItems) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final wishlistJson = wishlistItems.map((item) => item.toJson()).toList();
      await prefs.setString(_wishlistKey, jsonEncode(wishlistJson));
    } catch (e) {
      if (kDebugMode) print('خطأ في حفظ المفضلة: $e');
    }
  }

  // تحميل المفضلة
  static Future<List<WishlistItem>> loadWishlist() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final wishlistString = prefs.getString(_wishlistKey);
      if (wishlistString != null) {
        final wishlistJson = jsonDecode(wishlistString) as List;
        return wishlistJson.map((item) => WishlistItem.fromJson(item)).toList();
      }
    } catch (e) {
      if (kDebugMode) print('خطأ في تحميل المفضلة: $e');
    }
    return [];
  }

  // حفظ المستخدم
  static Future<void> saveUser(user_model.User user) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_userKey, jsonEncode(user.toJson()));
    } catch (e) {
      if (kDebugMode) print('خطأ في حفظ المستخدم: $e');
    }
  }

  // تحميل المستخدم
  static Future<user_model.User?> loadUser() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final userString = prefs.getString(_userKey);
      if (userString != null) {
        final userJson = jsonDecode(userString);
        return user_model.User.fromJson(userJson);
      }
    } catch (e) {
      if (kDebugMode) print('خطأ في تحميل المستخدم: $e');
    }
    return null;
  }

  // مسح بيانات المستخدم
  static Future<void> clearUser() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_userKey);
    } catch (e) {
      if (kDebugMode) print('خطأ في مسح بيانات المستخدم: $e');
    }
  }

  // تاريخ البحث

  static Future<void> saveSearchHistory(List<String> history) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setStringList(_searchHistoryKey, history);
    } catch (e) {
      if (kDebugMode) print('خطأ في حفظ تاريخ البحث: $e');
    }
  }

  static Future<List<String>> loadSearchHistory() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return prefs.getStringList(_searchHistoryKey) ?? [];
    } catch (e) {
      if (kDebugMode) print('خطأ في تحميل تاريخ البحث: $e');
      return [];
    }
  }

  static Future<void> clearSearchHistory() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_searchHistoryKey);
    } catch (e) {
      if (kDebugMode) print('خطأ في مسح تاريخ البحث: $e');
    }
  }

  // إعدادات الإشعارات
  static const String _notificationSettingsKey = 'notification_settings';

  static Future<void> saveNotificationSettings(
    Map<String, dynamic> settings,
  ) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_notificationSettingsKey, jsonEncode(settings));
    } catch (e) {
      if (kDebugMode) print('خطأ في حفظ إعدادات الإشعارات: $e');
    }
  }

  static Future<Map<String, dynamic>> loadNotificationSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final settingsString = prefs.getString(_notificationSettingsKey);
      if (settingsString != null) {
        return jsonDecode(settingsString);
      }
    } catch (e) {
      if (kDebugMode) print('خطأ في تحميل إعدادات الإشعارات: $e');
    }
    return {};
  }

  // إعدادات التطبيق العامة
  static const String _appSettingsKey = 'app_settings';

  static Future<void> saveAppSettings(Map<String, dynamic> settings) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_appSettingsKey, jsonEncode(settings));
    } catch (e) {
      if (kDebugMode) print('خطأ في حفظ إعدادات التطبيق: $e');
    }
  }

  static Future<Map<String, dynamic>> loadAppSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final settingsString = prefs.getString(_appSettingsKey);
      if (settingsString != null) {
        return jsonDecode(settingsString);
      }
    } catch (e) {
      if (kDebugMode) print('خطأ في تحميل إعدادات التطبيق: $e');
    }
    return {};
  }

  // مسح جميع البيانات
  static Future<void> clearAllData() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.clear();
    } catch (e) {
      if (kDebugMode) print('خطأ في مسح جميع البيانات: $e');
    }
  }

  // الحصول على حجم البيانات المخزنة
  static Future<int> getStorageSize() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final keys = prefs.getKeys();
      int totalSize = 0;

      for (final key in keys) {
        final value = prefs.get(key);
        if (value is String) {
          totalSize += value.length;
        }
      }

      return totalSize;
    } catch (e) {
      if (kDebugMode) print('خطأ في حساب حجم التخزين: $e');
      return 0;
    }
  }

  // إضافة عنصر لتاريخ البحث
  static Future<void> addToSearchHistory(String query) async {
    if (query.trim().isEmpty) return;

    try {
      final history = await loadSearchHistory();

      // إزالة العنصر إذا كان موجود
      history.remove(query);

      // إضافة العنصر في المقدمة
      history.insert(0, query);

      // الاحتفاظ بآخر 10 عناصر فقط
      if (history.length > 10) {
        history.removeRange(10, history.length);
      }

      await saveSearchHistory(history);
    } catch (e) {
      if (kDebugMode) print('خطأ في إضافة عنصر لتاريخ البحث: $e');
    }
  }

  // المنتجات
  static const String _productsKey = 'products';

  static Future<void> saveProducts(List<Product> products) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final productsJson = products.map((product) => product.toJson()).toList();
      await prefs.setString(_productsKey, jsonEncode(productsJson));
    } catch (e) {
      if (kDebugMode) print('خطأ في حفظ المنتجات: $e');
    }
  }

  static Future<List<Product>> loadProducts() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final productsString = prefs.getString(_productsKey);
      if (productsString != null) {
        final productsJson = jsonDecode(productsString) as List;
        return productsJson
            .map((product) => Product.fromJson(product))
            .toList();
      }
    } catch (e) {
      if (kDebugMode) print('خطأ في تحميل المنتجات: $e');
    }
    return [];
  }

  // الفئات
  static const String _categoriesKey = 'categories';

  static Future<void> saveCategories(
    List<category_model.Category> categories,
  ) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final categoriesJson = categories
          .map((category) => category.toJson())
          .toList();
      await prefs.setString(_categoriesKey, jsonEncode(categoriesJson));
    } catch (e) {
      if (kDebugMode) {
        print('خطأ في حفظ الفئات: $e');
      }
    }
  }

  static Future<List<category_model.Category>> loadCategories() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final categoriesString = prefs.getString(_categoriesKey);
      if (categoriesString != null) {
        final categoriesJson = jsonDecode(categoriesString) as List;
        return categoriesJson
            .map((category) => category_model.Category.fromJson(category))
            .toList();
      }
    } catch (e) {
      if (kDebugMode) {
        print('خطأ في تحميل الفئات: $e');
      }
    }
    return [];
  }

  // الطلبات
  static const String _ordersKey = 'orders';

  static Future<void> saveOrders(List<Order> orders) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final ordersJson = orders.map((order) => order.toJson()).toList();
      await prefs.setString(_ordersKey, jsonEncode(ordersJson));
    } catch (e) {
      if (kDebugMode) print('خطأ في حفظ الطلبات: $e');
    }
  }

  static Future<void> saveOrder(Order order) async {
    try {
      final orders = await loadOrders();
      final existingIndex = orders.indexWhere((o) => o.id == order.id);

      if (existingIndex != -1) {
        orders[existingIndex] = order;
      } else {
        orders.insert(0, order);
      }

      await saveOrders(orders);
    } catch (e) {
      if (kDebugMode) print('خطأ في حفظ الطلب: $e');
    }
  }

  static Future<List<Order>> loadOrders() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final ordersString = prefs.getString(_ordersKey);
      if (ordersString != null) {
        final ordersJson = jsonDecode(ordersString) as List;
        return ordersJson.map((order) => Order.fromJson(order)).toList();
      }
    } catch (e) {
      if (kDebugMode) print('خطأ في تحميل الطلبات: $e');
    }
    return [];
  }

  // حفظ قائمة الحسابات الإدارية
  static Future<void> saveAdminEmails(List<String> emails) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setStringList(_adminEmailsKey, emails);
  }

  // جلب قائمة الحسابات الإدارية
  static Future<List<String>> getAdminEmails() async {
    final prefs = await SharedPreferences.getInstance();
    final emails = prefs.getStringList(_adminEmailsKey);

    // إذا لم توجد قائمة محفوظة، أرجع القائمة الافتراضية
    if (emails == null || emails.isEmpty) {
      const defaultAdminEmails = [
        '<EMAIL>',
        '<EMAIL>',
      ];
      await saveAdminEmails(defaultAdminEmails);
      return defaultAdminEmails;
    }

    return emails;
  }

  // إضافة حساب إداري
  static Future<void> addAdminEmail(String email) async {
    final emails = await getAdminEmails();
    if (!emails.contains(email.toLowerCase())) {
      emails.add(email.toLowerCase());
      await saveAdminEmails(emails);
    }
  }

  // حذف حساب إداري
  static Future<void> removeAdminEmail(String email) async {
    final emails = await getAdminEmails();
    emails.remove(email.toLowerCase());
    await saveAdminEmails(emails);
  }

  // التحقق من كون البريد الإلكتروني حساب إداري
  static Future<bool> isAdminEmail(String email) async {
    final emails = await getAdminEmails();
    return emails.contains(email.toLowerCase());
  }

  // طرق الدفع
  static const String _paymentMethodsKey = 'payment_methods';

  static Future<void> savePaymentMethods(
    List<payment_model.PaymentMethod> methods,
  ) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final methodsJson = methods.map((method) => method.toJson()).toList();
      await prefs.setString(_paymentMethodsKey, jsonEncode(methodsJson));
    } catch (e) {
      if (kDebugMode) print('خطأ في حفظ طرق الدفع: $e');
    }
  }

  static Future<List<payment_model.PaymentMethod>> getPaymentMethods() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final methodsString = prefs.getString(_paymentMethodsKey);
      if (methodsString != null) {
        final methodsJson = jsonDecode(methodsString) as List;
        return methodsJson
            .map((method) => payment_model.PaymentMethod.fromJson(method))
            .toList();
      }
    } catch (e) {
      if (kDebugMode) print('خطأ في تحميل طرق الدفع: $e');
    }
    return [];
  }

  static Future<void> addPaymentMethod(
    payment_model.PaymentMethod method,
  ) async {
    try {
      final methods = await getPaymentMethods();
      methods.add(method);
      await savePaymentMethods(methods);
    } catch (e) {
      if (kDebugMode) print('خطأ في إضافة طريقة الدفع: $e');
    }
  }

  static Future<void> updatePaymentMethod(
    payment_model.PaymentMethod method,
  ) async {
    try {
      final methods = await getPaymentMethods();
      final index = methods.indexWhere((m) => m.id == method.id);
      if (index != -1) {
        methods[index] = method;
        await savePaymentMethods(methods);
      }
    } catch (e) {
      if (kDebugMode) print('خطأ في تحديث طريقة الدفع: $e');
    }
  }

  static Future<void> deletePaymentMethod(String methodId) async {
    try {
      final methods = await getPaymentMethods();
      methods.removeWhere((m) => m.id == methodId);
      await savePaymentMethods(methods);
    } catch (e) {
      if (kDebugMode) print('خطأ في حذف طريقة الدفع: $e');
    }
  }

  static Future<void> setDefaultPaymentMethod(String methodId) async {
    try {
      final methods = await getPaymentMethods();
      for (int i = 0; i < methods.length; i++) {
        methods[i] = methods[i].copyWith(isDefault: methods[i].id == methodId);
      }
      await savePaymentMethods(methods);
    } catch (e) {
      if (kDebugMode) print('خطأ في تعيين طريقة الدفع الافتراضية: $e');
    }
  }
}
