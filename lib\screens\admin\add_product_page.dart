import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'dart:convert';
import 'dart:async';
import 'package:file_picker/file_picker.dart';
import 'package:image/image.dart' as img;
import 'package:image_picker/image_picker.dart';
import '../../app_properties.dart';
import '../../models/product.dart';
import '../../models/category.dart' as category_model;
import '../../api_service_mock.dart';
import '../../services/firebase_platform_service.dart';
import '../../services/firebase_storage_service.dart';
import '../../services/image_processing_service.dart';

class AddProductPage extends StatefulWidget {
  const AddProductPage({super.key});

  @override
  State<AddProductPage> createState() => _AddProductPageState();
}

class _AddProductPageState extends State<AddProductPage> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _priceController = TextEditingController();
  final _stockController = TextEditingController();
  final _brandController = TextEditingController();
  final _skuController = TextEditingController();
  final _imageUrlController = TextEditingController();

  List<category_model.Category> _categories = [];
  List<Map<String, dynamic>> _brands = [];
  String? _selectedCategoryId;
  String? _selectedBrandId;
  bool _isLoading = false;
  bool _isActive = true;
  bool _isFeatured = false;

  // متغيرات الصورة
  Uint8List? _selectedImageBytes;
  String? _selectedImageName;
  String? _selectedImageType;

  // متغيرات الصور المتعددة
  List<ProcessedImage> _selectedImages = [];
  bool _useMultipleImages = false;
  bool _isProcessingImages = false; // نوع الصورة (png/jpeg)
  bool _useImageUpload = true; // true للرفع المباشر، false لرابط

  @override
  void initState() {
    super.initState();
    _loadCategories();
    _loadBrands();
  }

  @override
  void dispose() {
    _nameController.dispose();
    _descriptionController.dispose();
    _priceController.dispose();
    _stockController.dispose();
    _brandController.dispose();
    _skuController.dispose();
    _imageUrlController.dispose();
    super.dispose();
  }

  Future<void> _loadCategories() async {
    try {
      if (kDebugMode) print('🔄 تحميل الفئات من Firebase...');

      // محاولة جلب الفئات من Firebase Web Service أولاً
      try {
        final categoriesData = await FirebasePlatformService.getCategories();
        if (kDebugMode) {
          print('✅ تم جلب ${categoriesData.length} فئة من Firebase');
        }

        // تحويل البيانات إلى كائنات Category
        final categories = categoriesData
            .map((data) => category_model.Category.fromJson(data))
            .toList();

        setState(() {
          _categories = categories;
        });
        return;
      } catch (firebaseError) {
        if (kDebugMode) print('⚠️ فشل جلب الفئات من Firebase: $firebaseError');
      }

      // في حالة فشل Firebase، استخدم البيانات المحلية
      final categories = await ApiService.getCategories();
      if (kDebugMode) {
        print('📂 تم جلب ${categories.length} فئة من البيانات المحلية');
      }
      setState(() {
        _categories = categories;
      });
    } catch (e) {
      if (kDebugMode) print('❌ خطأ في تحميل الفئات: $e');
      // في حالة فشل كل شيء، استخدم فئات افتراضية من PredefinedCategories
      setState(() {
        _categories = category_model.PredefinedCategories.getCategories();
      });
    }
  }

  Future<void> _loadBrands() async {
    try {
      if (kDebugMode) print('🔄 تحميل العلامات التجارية من Firebase...');

      // محاولة جلب العلامات التجارية من Firebase Web Service
      try {
        final brandsData = await FirebasePlatformService.getBrands();
        if (kDebugMode) {
          print('✅ تم جلب ${brandsData.length} علامة تجارية من Firebase');
        }

        setState(() {
          _brands = brandsData;
        });
        return;
      } catch (firebaseError) {
        if (kDebugMode) {
          print('⚠️ فشل جلب العلامات التجارية من Firebase: $firebaseError');
        }
      }

      // في حالة فشل Firebase، استخدم قائمة فارغة
      setState(() {
        _brands = [];
      });
    } catch (e) {
      if (kDebugMode) print('❌ خطأ في تحميل العلامات التجارية: $e');
      setState(() {
        _brands = [];
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.scaffoldBackground,
      appBar: AppBar(
        title: const Text('إضافة منتج جديد'),
        backgroundColor: AppColors.primaryColor,
        foregroundColor: AppColors.white,
        elevation: 0,
        centerTitle: true,
        actions: [
          TextButton(
            onPressed: _isLoading ? null : _saveProduct,
            child: const Text(
              'حفظ',
              style: TextStyle(
                color: AppColors.white,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      ),
      body: Form(
        key: _formKey,
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // معلومات أساسية
              _buildSectionCard(
                title: 'المعلومات الأساسية',
                children: [
                  _buildTextField(
                    controller: _nameController,
                    label: 'اسم المنتج',
                    hint: 'أدخل اسم المنتج',
                    validator: (value) {
                      if (value == null || value.trim().isEmpty) {
                        return 'يرجى إدخال اسم المنتج';
                      }
                      return null;
                    },
                  ),
                  const SizedBox(height: 16),
                  _buildTextField(
                    controller: _descriptionController,
                    label: 'وصف المنتج',
                    hint: 'أدخل وصف تفصيلي للمنتج',
                    maxLines: 4,
                    validator: (value) {
                      if (value == null || value.trim().isEmpty) {
                        return 'يرجى إدخال وصف المنتج';
                      }
                      return null;
                    },
                  ),
                  const SizedBox(height: 16),
                  _buildBrandDropdown(),
                  const SizedBox(height: 16),
                  _buildTextField(
                    controller: _skuController,
                    label: 'رمز المنتج (SKU)',
                    hint: 'أدخل رمز المنتج الفريد',
                  ),
                ],
              ),

              const SizedBox(height: 20),

              // السعر والمخزون
              _buildSectionCard(
                title: 'السعر والمخزون',
                children: [
                  Row(
                    children: [
                      Expanded(
                        child: _buildTextField(
                          controller: _priceController,
                          label: 'السعر (IQD)',
                          hint: '0',
                          keyboardType: TextInputType.number,
                          validator: (value) {
                            if (value == null || value.trim().isEmpty) {
                              return 'يرجى إدخال السعر';
                            }
                            if (double.tryParse(value) == null) {
                              return 'يرجى إدخال رقم صحيح';
                            }
                            return null;
                          },
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: _buildTextField(
                          controller: _stockController,
                          label: 'الكمية المتوفرة',
                          hint: '0',
                          keyboardType: TextInputType.number,
                          validator: (value) {
                            if (value == null || value.trim().isEmpty) {
                              return 'يرجى إدخال الكمية';
                            }
                            if (int.tryParse(value) == null) {
                              return 'يرجى إدخال رقم صحيح';
                            }
                            return null;
                          },
                        ),
                      ),
                    ],
                  ),
                ],
              ),

              const SizedBox(height: 20),

              // الصور
              _buildSectionCard(
                title: 'صور المنتج',
                children: [_buildImageSection()],
              ),

              const SizedBox(height: 20),

              // الفئة والإعدادات
              _buildSectionCard(
                title: 'الفئة والإعدادات',
                children: [
                  _buildCategoryDropdown(),
                  const SizedBox(height: 16),
                  _buildSwitchTile(
                    title: 'منتج نشط',
                    subtitle: 'سيظهر المنتج للعملاء',
                    value: _isActive,
                    onChanged: (value) => setState(() => _isActive = value),
                  ),
                  _buildSwitchTile(
                    title: 'منتج مميز',
                    subtitle: 'سيظهر في قسم المنتجات المميزة',
                    value: _isFeatured,
                    onChanged: (value) => setState(() => _isFeatured = value),
                  ),
                ],
              ),

              const SizedBox(height: 32),

              // أزرار الحفظ والإلغاء
              Row(
                children: [
                  Expanded(
                    child: OutlinedButton(
                      onPressed: _isLoading
                          ? null
                          : () => Navigator.pop(context),
                      child: const Text('إلغاء'),
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: ElevatedButton(
                      onPressed: _isLoading ? null : _saveProduct,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: AppColors.primaryColor,
                        foregroundColor: AppColors.white,
                      ),
                      child: _isLoading
                          ? const SizedBox(
                              width: 20,
                              height: 20,
                              child: CircularProgressIndicator(
                                strokeWidth: 2,
                                valueColor: AlwaysStoppedAnimation<Color>(
                                  AppColors.white,
                                ),
                              ),
                            )
                          : const Text('حفظ المنتج'),
                    ),
                  ),
                ],
              ),

              const SizedBox(height: 20),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSectionCard({
    required String title,
    required List<Widget> children,
  }) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: AppColors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: const TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: AppColors.primaryColor,
            ),
          ),
          const SizedBox(height: 16),
          ...children,
        ],
      ),
    );
  }

  Widget _buildTextField({
    required TextEditingController controller,
    required String label,
    String? hint,
    int maxLines = 1,
    TextInputType? keyboardType,
    String? Function(String?)? validator,
  }) {
    return TextFormField(
      controller: controller,
      decoration: InputDecoration(
        labelText: label,
        hintText: hint,
        border: const OutlineInputBorder(),
        focusedBorder: const OutlineInputBorder(
          borderSide: BorderSide(color: AppColors.primaryColor),
        ),
      ),
      maxLines: maxLines,
      keyboardType: keyboardType,
      validator: validator,
    );
  }

  Widget _buildCategoryDropdown() {
    return DropdownButtonFormField<String>(
      value: _selectedCategoryId,
      decoration: const InputDecoration(
        labelText: 'الفئة',
        border: OutlineInputBorder(),
        focusedBorder: OutlineInputBorder(
          borderSide: BorderSide(color: AppColors.primaryColor),
        ),
      ),
      hint: const Text('اختر فئة المنتج'),
      items: _categories.map((category) {
        return DropdownMenuItem(value: category.id, child: Text(category.name));
      }).toList(),
      onChanged: (value) {
        setState(() {
          _selectedCategoryId = value;
        });
      },
      validator: (value) {
        if (value == null || value.isEmpty) {
          return 'يرجى اختيار فئة المنتج';
        }
        return null;
      },
    );
  }

  Widget _buildBrandDropdown() {
    return DropdownButtonFormField<String>(
      value: _selectedBrandId,
      decoration: const InputDecoration(
        labelText: 'العلامة التجارية',
        border: OutlineInputBorder(),
        focusedBorder: OutlineInputBorder(
          borderSide: BorderSide(color: AppColors.primaryColor),
        ),
      ),
      hint: const Text('اختر العلامة التجارية'),
      items: _brands.map((brand) {
        return DropdownMenuItem(
          value: brand['id'].toString(),
          child: Text(brand['name'] ?? 'غير محدد'),
        );
      }).toList(),
      onChanged: (value) {
        setState(() {
          _selectedBrandId = value;
        });
      },
      validator: (value) {
        if (value == null || value.isEmpty) {
          return 'يرجى اختيار العلامة التجارية';
        }
        return null;
      },
    );
  }

  Widget _buildSwitchTile({
    required String title,
    required String subtitle,
    required bool value,
    required ValueChanged<bool> onChanged,
  }) {
    return SwitchListTile(
      title: Text(title, style: const TextStyle(fontWeight: FontWeight.w500)),
      subtitle: Text(
        subtitle,
        style: const TextStyle(color: AppColors.secondaryText, fontSize: 12),
      ),
      value: value,
      onChanged: onChanged,
      activeColor: AppColors.primaryColor,
      contentPadding: EdgeInsets.zero,
    );
  }

  Widget _buildImageSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // خيارات رفع الصورة
        Column(
          children: [
            Row(
              children: [
                Expanded(
                  child: RadioListTile<bool>(
                    title: const Text('رفع صورة مباشر'),
                    subtitle: const Text('PNG, JPEG, WebP'),
                    value: true,
                    groupValue: _useImageUpload,
                    onChanged: (value) {
                      setState(() {
                        _useImageUpload = value!;
                        if (_useImageUpload) {
                          _imageUrlController.clear();
                        } else {
                          _selectedImageBytes = null;
                          _selectedImageName = null;
                          _selectedImageType = null;
                          _selectedImages.clear();
                        }
                      });
                    },
                    contentPadding: EdgeInsets.zero,
                  ),
                ),
                Expanded(
                  child: RadioListTile<bool>(
                    title: const Text('رابط صورة'),
                    subtitle: const Text('URL مباشر'),
                    value: false,
                    groupValue: _useImageUpload,
                    onChanged: (value) {
                      setState(() {
                        _useImageUpload = value!;
                        if (_useImageUpload) {
                          _imageUrlController.clear();
                        } else {
                          _selectedImageBytes = null;
                          _selectedImageName = null;
                          _selectedImageType = null;
                          _selectedImages.clear();
                        }
                      });
                    },
                    contentPadding: EdgeInsets.zero,
                  ),
                ),
              ],
            ),

            // خيار الصور المتعددة (فقط عند الرفع المباشر)
            if (_useImageUpload) ...[
              const SizedBox(height: 8),
              Row(
                children: [
                  Expanded(
                    child: CheckboxListTile(
                      title: const Text('رفع صور متعددة'),
                      subtitle: const Text('حتى 3 صور للمنتج'),
                      value: _useMultipleImages,
                      onChanged: (value) {
                        setState(() {
                          _useMultipleImages = value ?? false;
                          if (!_useMultipleImages && _selectedImages.length > 1) {
                            // الاحتفاظ بالصورة الأولى فقط
                            final firstImage = _selectedImages.first;
                            _selectedImages = [firstImage];
                            _selectedImageBytes = firstImage.bytes;
                            _selectedImageName = firstImage.name;
                            _selectedImageType = firstImage.format;
                          }
                        });
                      },
                      contentPadding: EdgeInsets.zero,
                      controlAffinity: ListTileControlAffinity.leading,
                    ),
                  ),
                ],
              ),
            ],
          ],
        ),

        const SizedBox(height: 16),

        // قسم رفع الصورة المباشر
        if (_useImageUpload) ...[
          _buildImageUploadSection(),
        ] else ...[
          _buildImageUrlSection(),
        ],
      ],
    );
  }

  Widget _buildImageUploadSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // عرض الصورة الرئيسية
        Container(
          width: double.infinity,
          height: 200,
          decoration: BoxDecoration(
            border: Border.all(
              color: _selectedImageBytes != null
                  ? AppColors.primaryColor
                  : AppColors.grey.withValues(alpha: 0.3),
              width: 2,
              style: BorderStyle.solid,
            ),
            borderRadius: BorderRadius.circular(8),
          ),
          child: _selectedImageBytes != null
              ? Stack(
                  children: [
                    ClipRRect(
                      borderRadius: BorderRadius.circular(6),
                      child: Image.memory(
                        _selectedImageBytes!,
                        width: double.infinity,
                        height: double.infinity,
                        fit: BoxFit.cover,
                      ),
                    ),
                    Positioned(
                      top: 8,
                      right: 8,
                      child: Container(
                        decoration: const BoxDecoration(
                          color: AppColors.errorColor,
                          shape: BoxShape.circle,
                        ),
                        child: IconButton(
                          icon: const Icon(
                            Icons.close,
                            color: AppColors.white,
                            size: 20,
                          ),
                          onPressed: () {
                            setState(() {
                              _selectedImageBytes = null;
                              _selectedImageName = null;
                              _selectedImageType = null;
                            });
                          },
                        ),
                      ),
                    ),
                  ],
                )
              : Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    const Icon(
                      Icons.add_photo_alternate,
                      size: 48,
                      color: AppColors.primaryColor,
                    ),
                    const SizedBox(height: 16),
                    const Text(
                      'اختر صورة المنتج',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w500,
                        color: AppColors.primaryColor,
                      ),
                    ),
                    const SizedBox(height: 8),
                    const Text(
                      'PNG, JPEG - جودة محسنة تلقائياً',
                      style: TextStyle(
                        fontSize: 12,
                        color: AppColors.secondaryText,
                      ),
                    ),
                    const SizedBox(height: 16),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                      children: [
                        ElevatedButton.icon(
                          onPressed: _isProcessingImages ? null : _pickImage,
                          icon: _isProcessingImages
                            ? const SizedBox(
                                width: 16,
                                height: 16,
                                child: CircularProgressIndicator(
                                  strokeWidth: 2,
                                  valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                                ),
                              )
                            : const Icon(Icons.photo_library, size: 20),
                          label: const Text('المعرض'),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: AppColors.primaryColor,
                            foregroundColor: AppColors.white,
                            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                          ),
                        ),
                        ElevatedButton.icon(
                          onPressed: _isProcessingImages ? null : _pickImageFromCamera,
                          icon: _isProcessingImages
                            ? const SizedBox(
                                width: 16,
                                height: 16,
                                child: CircularProgressIndicator(
                                  strokeWidth: 2,
                                  valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                                ),
                              )
                            : const Icon(Icons.camera_alt, size: 20),
                          label: const Text('الكاميرا'),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: AppColors.secondaryColor,
                            foregroundColor: AppColors.white,
                            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
        ),

        if (_selectedImageName != null) ...[
          const SizedBox(height: 8),
          Text(
            _useMultipleImages
              ? 'الصورة الرئيسية: $_selectedImageName'
              : 'الملف المختار: $_selectedImageName',
            style: const TextStyle(
              fontSize: 12,
              color: AppColors.secondaryText,
            ),
          ),
        ],

        // عرض الصور المتعددة
        if (_useMultipleImages && _selectedImages.length > 1) ...[
          const SizedBox(height: 16),
          Text(
            'الصور الإضافية (${_selectedImages.length - 1}):',
            style: const TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w600,
              color: AppColors.primaryColor,
            ),
          ),
          const SizedBox(height: 8),
          SizedBox(
            height: 100,
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              itemCount: _selectedImages.length,
              itemBuilder: (context, index) {
                final image = _selectedImages[index];
                final isMainImage = _selectedImageBytes == image.bytes;

                return Container(
                  width: 100,
                  margin: const EdgeInsets.only(right: 8),
                  decoration: BoxDecoration(
                    border: Border.all(
                      color: isMainImage ? AppColors.accentColor : AppColors.grey.withValues(alpha: 0.3),
                      width: isMainImage ? 3 : 1,
                    ),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Stack(
                    children: [
                      ClipRRect(
                        borderRadius: BorderRadius.circular(6),
                        child: Image.memory(
                          image.bytes,
                          width: 100,
                          height: 100,
                          fit: BoxFit.cover,
                        ),
                      ),

                      // مؤشر الصورة الرئيسية
                      if (isMainImage)
                        Positioned(
                          top: 4,
                          left: 4,
                          child: Container(
                            padding: const EdgeInsets.all(2),
                            decoration: const BoxDecoration(
                              color: AppColors.accentColor,
                              shape: BoxShape.circle,
                            ),
                            child: const Icon(
                              Icons.star,
                              color: AppColors.white,
                              size: 12,
                            ),
                          ),
                        ),

                      // أزرار التحكم
                      Positioned(
                        top: 4,
                        right: 4,
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            if (!isMainImage)
                              GestureDetector(
                                onTap: () => _setMainImage(index),
                                child: Container(
                                  padding: const EdgeInsets.all(2),
                                  decoration: const BoxDecoration(
                                    color: AppColors.primaryColor,
                                    shape: BoxShape.circle,
                                  ),
                                  child: const Icon(
                                    Icons.star_border,
                                    color: AppColors.white,
                                    size: 12,
                                  ),
                                ),
                              ),
                            const SizedBox(width: 2),
                            GestureDetector(
                              onTap: () => _removeImage(index),
                              child: Container(
                                padding: const EdgeInsets.all(2),
                                decoration: const BoxDecoration(
                                  color: AppColors.errorColor,
                                  shape: BoxShape.circle,
                                ),
                                child: const Icon(
                                  Icons.close,
                                  color: AppColors.white,
                                  size: 12,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),

                      // معلومات الصورة
                      Positioned(
                        bottom: 0,
                        left: 0,
                        right: 0,
                        child: Container(
                          padding: const EdgeInsets.all(2),
                          decoration: BoxDecoration(
                            color: AppColors.black.withValues(alpha: 0.7),
                            borderRadius: const BorderRadius.only(
                              bottomLeft: Radius.circular(6),
                              bottomRight: Radius.circular(6),
                            ),
                          ),
                          child: Text(
                            '${image.dimensions}\n${image.sizeFormatted}',
                            style: const TextStyle(
                              color: AppColors.white,
                              fontSize: 8,
                            ),
                            textAlign: TextAlign.center,
                          ),
                        ),
                      ),
                    ],
                  ),
                );
              },
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildImageUrlSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildTextField(
          controller: _imageUrlController,
          label: 'رابط الصورة الرئيسية',
          hint: 'https://example.com/image.jpg',
          validator: (value) {
            if (!_useImageUpload && (value == null || value.trim().isEmpty)) {
              return 'يرجى إدخال رابط الصورة';
            }
            if (!_useImageUpload && value != null && value.isNotEmpty) {
              final uri = Uri.tryParse(value);
              if (uri == null || !uri.hasAbsolutePath) {
                return 'يرجى إدخال رابط صحيح';
              }
            }
            return null;
          },
        ),
        const SizedBox(height: 12),
        const Text(
          'أمثلة على روابط الصور:',
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w500,
            color: AppColors.primaryText,
          ),
        ),
        const SizedBox(height: 8),
        Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: AppColors.lightGrey.withValues(alpha: 0.3),
            borderRadius: BorderRadius.circular(8),
          ),
          child: const Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                '• https://images.unsplash.com/photo-1574258495973-f010dfbb5371?w=500&h=500',
                style: TextStyle(fontSize: 12, color: AppColors.secondaryText),
              ),
              SizedBox(height: 4),
              Text(
                '• https://example.com/product-image.jpg',
                style: TextStyle(fontSize: 12, color: AppColors.secondaryText),
              ),
              SizedBox(height: 4),
              Text(
                '• تأكد من أن الرابط يؤدي مباشرة للصورة',
                style: TextStyle(fontSize: 12, color: AppColors.warningColor),
              ),
            ],
          ),
        ),
        if (_imageUrlController.text.isNotEmpty) ...[
          const SizedBox(height: 16),
          const Text(
            'معاينة الصورة:',
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w500,
              color: AppColors.primaryText,
            ),
          ),
          const SizedBox(height: 8),
          Container(
            width: double.infinity,
            height: 200,
            decoration: BoxDecoration(
              border: Border.all(color: AppColors.grey.withValues(alpha: 0.3)),
              borderRadius: BorderRadius.circular(8),
            ),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(7),
              child: Image.network(
                _imageUrlController.text,
                fit: BoxFit.cover,
                errorBuilder: (context, error, stackTrace) {
                  return const Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.error,
                          color: AppColors.errorColor,
                          size: 40,
                        ),
                        SizedBox(height: 8),
                        Text(
                          'فشل في تحميل الصورة',
                          style: TextStyle(color: AppColors.errorColor),
                        ),
                      ],
                    ),
                  );
                },
                loadingBuilder: (context, child, loadingProgress) {
                  if (loadingProgress == null) return child;
                  return const Center(child: CircularProgressIndicator());
                },
              ),
            ),
          ),
        ],
      ],
    );
  }

  Future<void> _pickImage() async {
    if (_isProcessingImages) return; // منع التشغيل المتعدد

    setState(() {
      _isProcessingImages = true;
    });

    try {
      if (kDebugMode) {
        debugPrint('🔄 بدء عملية اختيار الصورة بجودة عالية...');
      }

      // عرض مؤشر التحميل المبسط
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('جاري اختيار الصور...'),
            backgroundColor: AppColors.primaryColor,
            duration: Duration(seconds: 5),
          ),
        );
      }

      // استخدام خدمة معالجة الصور المحسنة
      final imageResult = await ImageProcessingService.pickImageFromGallery(
        allowMultiple: _useMultipleImages,
        maxImages: _useMultipleImages ? 3 : 1, // تقليل العدد أكثر لتحسين الأداء
      );

      if (imageResult != null && imageResult.isNotEmpty) {
        if (_useMultipleImages) {
          // إضافة الصور الجديدة إلى القائمة
          setState(() {
            _selectedImages.addAll(imageResult.images);
            // تحديد الصورة الرئيسية إذا لم تكن محددة
            if (_selectedImageBytes == null && _selectedImages.isNotEmpty) {
              final firstImage = _selectedImages.first;
              _selectedImageBytes = firstImage.bytes;
              _selectedImageName = firstImage.name;
              _selectedImageType = firstImage.format;
            }
          });

          if (kDebugMode) {
            debugPrint('✅ تم اختيار ${imageResult.images.length} صورة');
            debugPrint('📊 إجمالي الصور: ${_selectedImages.length}');
          }

          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(
                  'تم اختيار ${imageResult.images.length} صورة - إجمالي: ${_selectedImages.length}',
                ),
                backgroundColor: AppColors.accentColor,
                duration: const Duration(seconds: 3),
              ),
            );
          }
        } else {
          // صورة واحدة فقط
          final processedImage = imageResult.firstImage!;

          setState(() {
            _selectedImageBytes = processedImage.bytes;
            _selectedImageName = processedImage.name;
            _selectedImageType = processedImage.format;
            _selectedImages = [processedImage];
          });

          if (kDebugMode) {
            debugPrint('✅ تم اختيار الصورة بنجاح');
            debugPrint('📊 معلومات الصورة: $processedImage');
          }

          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(
                  'تم اختيار الصورة: ${processedImage.sizeFormatted} - ${processedImage.dimensions} - جودة ${processedImage.quality.name}',
                ),
                backgroundColor: AppColors.accentColor,
                duration: const Duration(seconds: 3),
              ),
            );
          }
        }
      } else {
        if (kDebugMode) {
          debugPrint('❌ لم يتم اختيار أي صورة');
        }
      }
    } catch (e) {
      if (kDebugMode) {
        debugPrint('❌ خطأ في اختيار الصورة: $e');
      }

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('فشل في معالجة الصور: ${e.toString()}'),
            backgroundColor: AppColors.errorColor,
            duration: const Duration(seconds: 5),
          ),
        );
      }
    } finally {
      // إخفاء مؤشر التحميل
      setState(() {
        _isProcessingImages = false;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).hideCurrentSnackBar();
      }
    }
  }

  Future<void> _pickImageFromCamera() async {
    if (_isProcessingImages) return; // منع التشغيل المتعدد

    setState(() {
      _isProcessingImages = true;
    });

    try {
      if (kDebugMode) {
        debugPrint('📷 بدء التقاط صورة من الكاميرا بجودة عالية...');
      }

      // تنظيف الذاكرة قبل استخدام الكاميرا
      if (_selectedImages.length > 3) {
        if (kDebugMode) {
          debugPrint('🧹 تنظيف الصور القديمة لتوفير الذاكرة...');
        }
        // الاحتفاظ بآخر 3 صور فقط
        _selectedImages = _selectedImages.take(3).toList();
        setState(() {});

        // إجبار garbage collection
        await Future.delayed(const Duration(milliseconds: 100));
      }

      // عرض مؤشر التحميل المبسط
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('جاري التقاط الصورة...'),
            backgroundColor: AppColors.primaryColor,
            duration: Duration(seconds: 5),
          ),
        );
      }

      // استخدام خدمة معالجة الصور المحسنة للكاميرا
      final imageResult = await ImageProcessingService.pickImageFromCamera();

      if (imageResult != null && imageResult.isNotEmpty) {
        final processedImage = imageResult.firstImage!;

        setState(() {
          if (_useMultipleImages) {
            // إضافة الصورة إلى القائمة
            _selectedImages.add(processedImage);
            // تحديد الصورة الرئيسية إذا لم تكن محددة
            if (_selectedImageBytes == null) {
              _selectedImageBytes = processedImage.bytes;
              _selectedImageName = processedImage.name;
              _selectedImageType = processedImage.format;
            }
          } else {
            // صورة واحدة فقط
            _selectedImageBytes = processedImage.bytes;
            _selectedImageName = processedImage.name;
            _selectedImageType = processedImage.format;
            _selectedImages = [processedImage];
          }
        });

        if (kDebugMode) {
          debugPrint('✅ تم التقاط الصورة بنجاح');
          debugPrint('📊 معلومات الصورة: $processedImage');
        }

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                _useMultipleImages
                  ? 'تم التقاط الصورة - إجمالي: ${_selectedImages.length}'
                  : 'تم التقاط الصورة: ${processedImage.sizeFormatted} - ${processedImage.dimensions} - جودة ${processedImage.quality.name}',
              ),
              backgroundColor: AppColors.accentColor,
              duration: const Duration(seconds: 3),
            ),
          );
        }
      } else {
        if (kDebugMode) {
          debugPrint('❌ لم يتم التقاط أي صورة');
        }
      }
    } catch (e) {
      if (kDebugMode) {
        debugPrint('❌ خطأ في التقاط الصورة من الكاميرا: $e');
      }

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('فشل في التقاط الصورة: ${e.toString()}'),
            backgroundColor: AppColors.errorColor,
            action: SnackBarAction(
              label: 'إعادة المحاولة',
              textColor: Colors.white,
              onPressed: () => _pickImageFromCamera(),
            ),
          ),
        );
      }
    } finally {
      // إخفاء مؤشر التحميل
      setState(() {
        _isProcessingImages = false;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).hideCurrentSnackBar();
      }
    }
  }

  void _removeImage(int index) {
    setState(() {
      if (index >= 0 && index < _selectedImages.length) {
        _selectedImages.removeAt(index);

        // إذا تم حذف الصورة الرئيسية، اختر صورة أخرى
        if (_selectedImages.isNotEmpty) {
          final newMainImage = _selectedImages.first;
          _selectedImageBytes = newMainImage.bytes;
          _selectedImageName = newMainImage.name;
          _selectedImageType = newMainImage.format;
        } else {
          _selectedImageBytes = null;
          _selectedImageName = null;
          _selectedImageType = null;
        }
      }
    });

    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('تم حذف الصورة - المتبقي: ${_selectedImages.length}'),
          backgroundColor: AppColors.warningColor,
          duration: const Duration(seconds: 2),
        ),
      );
    }
  }

  void _setMainImage(int index) {
    if (index >= 0 && index < _selectedImages.length) {
      setState(() {
        final selectedImage = _selectedImages[index];
        _selectedImageBytes = selectedImage.bytes;
        _selectedImageName = selectedImage.name;
        _selectedImageType = selectedImage.format;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم تحديد الصورة الرئيسية'),
            backgroundColor: AppColors.accentColor,
            duration: Duration(seconds: 2),
          ),
        );
      }
    }
  }

  Future<void> _pickImageWithImagePicker() async {
    try {
      if (kDebugMode) {
        debugPrint('🌐 استخدام ImagePicker لاختيار الصورة...');
      }

      final ImagePicker picker = ImagePicker();

      // اختيار صورة من المعرض بالجودة الأصلية
      final XFile? image = await picker.pickImage(
        source: ImageSource.gallery,
        imageQuality: 100, // جودة كاملة
        // إزالة maxWidth و maxHeight للحفاظ على الدقة الأصلية
      );

      if (image != null) {
        if (kDebugMode) {
          debugPrint('📄 تم اختيار الصورة: ${image.name}');
          debugPrint('📏 حجم الملف: ${await image.length()} بايت');
        }

        // قراءة بيانات الصورة
        final bytes = await image.readAsBytes();

        // التحقق من حجم الملف
        if (bytes.length > 20 * 1024 * 1024) {
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('حجم الصورة كبير جداً. الحد الأقصى 20MB'),
                backgroundColor: AppColors.errorColor,
              ),
            );
          }
          return;
        }

        // تحديد نوع الصورة من الاسم
        String imageType = 'jpeg';
        if (image.name.toLowerCase().endsWith('.png')) {
          imageType = 'png';
        }

        // استخدام الصورة بجودتها الأصلية بدون ضغط
        setState(() {
          _selectedImageBytes = bytes;
          _selectedImageName = image.name;
          _selectedImageType = imageType;
        });

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('تم اختيار الصورة: ${image.name}'),
              backgroundColor: AppColors.primaryColor,
            ),
          );
        }
      } else {
        if (kDebugMode) {
          debugPrint('❌ لم يتم اختيار أي صورة');
        }
      }
    } catch (e) {
      if (kDebugMode) {
        debugPrint('❌ خطأ في ImagePicker: $e');
      }

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('فشل في اختيار الصورة: ${e.toString()}'),
            backgroundColor: AppColors.errorColor,
            action: SnackBarAction(
              label: 'إعادة المحاولة',
              textColor: Colors.white,
              onPressed: () => _pickImageWithImagePicker(),
            ),
          ),
        );
      }
    }
  }

  Future<void> _pickImageForWeb() async {
    try {
      if (kDebugMode) {
        debugPrint('🌐 استخدام HTML Input مباشر لاختيار الصورة...');
      }

      // استدعاء دالة JavaScript مباشرة
      final result = await _callHtmlInputPickFile();

      if (result != null) {
        final name = result['name'] as String;
        final size = result['size'] as int;
        final dataUrl = result['data'] as String;

        if (kDebugMode) {
          debugPrint('📄 تفاصيل الملف:');
          debugPrint('  - الاسم: $name');
          debugPrint('  - الحجم: $size بايت');
        }

        // التحقق من حجم الملف
        if (size > 20 * 1024 * 1024) {
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('حجم الصورة كبير جداً. الحد الأقصى 20MB'),
                backgroundColor: AppColors.errorColor,
              ),
            );
          }
          return;
        }

        // استخراج البيانات من Data URL
        final base64Data = dataUrl.split(',')[1];
        final bytes = base64Decode(base64Data);

        // تحديد نوع الصورة
        String imageType = 'jpeg';
        if (name.toLowerCase().endsWith('.png')) {
          imageType = 'png';
        }

        // استخدام الصورة بجودتها الأصلية بدون ضغط
        setState(() {
          _selectedImageBytes = bytes;
          _selectedImageName = name;
          _selectedImageType = imageType;
        });

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('تم اختيار الصورة: $name'),
              backgroundColor: AppColors.primaryColor,
            ),
          );
        }
      } else {
        // لم يتم اختيار أي ملف أو فشل FilePicker
        if (kDebugMode) {
          debugPrint('❌ لم يتم اختيار أي ملف أو فشل FilePicker');
        }
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: const Text(
                'لم يتم اختيار أي صورة. يرجى المحاولة مرة أخرى.',
              ),
              backgroundColor: AppColors.warningColor,
              action: SnackBarAction(
                label: 'إعادة المحاولة',
                textColor: Colors.white,
                onPressed: () => _pickImageForWeb(),
              ),
            ),
          );
        }
      }
    } catch (e) {
      if (kDebugMode) {
        debugPrint('❌ خطأ في اختيار الصورة عبر JavaScript: $e');
      }

      String errorMessage = 'فشل في اختيار الصورة';
      if (e.toString().contains('LateInitializationError')) {
        errorMessage =
            'خطأ في تهيئة النظام. يرجى إعادة تحميل الصفحة والمحاولة مرة أخرى.';
      } else if (e.toString().contains('TimeoutException')) {
        errorMessage = 'انتهت مهلة اختيار الملف. يرجى المحاولة مرة أخرى.';
      } else if (e.toString().contains('PlatformException')) {
        errorMessage = 'خطأ في النظام. تأكد من أن المتصفح يدعم رفع الملفات.';
      }

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(errorMessage),
            backgroundColor: AppColors.errorColor,
            duration: const Duration(seconds: 5),
            action: SnackBarAction(
              label: 'إعادة المحاولة',
              textColor: Colors.white,
              onPressed: () => _pickImageForWeb(),
            ),
          ),
        );
      }
    }
  }

  Future<Map<String, dynamic>?> _callHtmlInputPickFile() async {
    try {
      if (kDebugMode) {
        debugPrint('🌐 محاولة استخدام FilePicker مع معالجة محسنة...');
      }

      // تأخير أطول للتهيئة في الويب
      if (kIsWeb) {
        await Future.delayed(const Duration(milliseconds: 2000));
      }

      // محاولة استخدام FilePicker مع معالجة شاملة للأخطاء
      FilePickerResult? result;

      try {
        result = await FilePicker.platform
            .pickFiles(
              type: FileType.custom,
              allowedExtensions: ['png', 'jpg', 'jpeg'],
              withData: true,
              allowMultiple: false,
              dialogTitle: 'اختر صورة المنتج',
            )
            .timeout(
              const Duration(seconds: 45),
              onTimeout: () {
                if (kDebugMode) {
                  debugPrint('⏰ انتهت مهلة FilePicker');
                }
                return null;
              },
            );
      } on Exception catch (e) {
        if (kDebugMode) {
          debugPrint('❌ خطأ في FilePicker: $e');
        }

        // إذا كان الخطأ LateInitializationError، أرجع null
        if (e.toString().contains('LateInitializationError')) {
          if (kDebugMode) {
            debugPrint('🔄 خطأ LateInitializationError - سيتم إرجاع null');
          }
          return null;
        }

        // للأخطاء الأخرى، أعد رمي الخطأ
        rethrow;
      }

      if (result != null && result.files.isNotEmpty) {
        final file = result.files.first;
        if (file.bytes != null) {
          final base64Data = base64Encode(file.bytes!);
          return {
            'name': file.name,
            'size': file.size,
            'data': 'data:${_getMimeType(file.extension)};base64,$base64Data',
          };
        }
      }

      return null;
    } catch (e) {
      if (kDebugMode) {
        debugPrint('❌ خطأ عام في اختيار الملف: $e');
      }
      return null;
    }
  }

  String _getMimeType(String? extension) {
    switch (extension?.toLowerCase()) {
      case 'png':
        return 'image/png';
      case 'jpg':
      case 'jpeg':
        return 'image/jpeg';
      default:
        return 'image/jpeg';
    }
  }



  Future<void> _saveProduct() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    // التحقق من وجود صورة
    if (_useImageUpload && _selectedImageBytes == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('يرجى اختيار صورة للمنتج'),
          backgroundColor: AppColors.errorColor,
        ),
      );
      return;
    }

    if (!_useImageUpload && _imageUrlController.text.trim().isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('يرجى إدخال رابط الصورة'),
          backgroundColor: AppColors.errorColor,
        ),
      );
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      // البحث عن اسم الفئة
      final selectedCategory = _categories.firstWhere(
        (cat) => cat.id == _selectedCategoryId,
        orElse: () => _categories.first,
      );

      // البحث عن اسم العلامة التجارية
      String? selectedBrandName;
      if (_selectedBrandId != null) {
        final selectedBrand = _brands.firstWhere(
          (brand) => brand['id'].toString() == _selectedBrandId,
          orElse: () => <String, dynamic>{},
        );
        selectedBrandName = selectedBrand['name'];
      }

      // إنشاء معرف المنتج أولاً
      final productId = DateTime.now().millisecondsSinceEpoch.toString();

      // تحديد مصدر الصور
      String imageUrl = '';
      List<String> imageUrls = [];

      if (_useImageUpload && _selectedImages.isNotEmpty) {
        if (kDebugMode) {
          debugPrint('🔄 رفع ${_selectedImages.length} صورة إلى Firebase Storage...');
        }

        // رفع جميع الصور
        final uploadedUrls = <String>[];

        for (int i = 0; i < _selectedImages.length; i++) {
          final image = _selectedImages[i];

          if (kDebugMode) {
            debugPrint('� رفع الصورة ${i + 1}/${_selectedImages.length}: ${image.name}');
            debugPrint('📏 حجم الصورة: ${image.sizeFormatted}');
          }

          final uploadedUrl = await FirebaseStorageService.uploadProductImage(
            productId,
            image.bytes,
            image.name,
          );

          if (uploadedUrl != null) {
            uploadedUrls.add(uploadedUrl);

            if (kDebugMode) {
              debugPrint('✅ تم رفع الصورة ${i + 1}: $uploadedUrl');
            }
          } else {
            if (kDebugMode) {
              debugPrint('❌ فشل في رفع الصورة ${i + 1}: ${image.name}');
            }
          }
        }

        if (uploadedUrls.isNotEmpty) {
          imageUrl = uploadedUrls.first; // الصورة الرئيسية
          imageUrls = uploadedUrls; // جميع الصور

          if (kDebugMode) {
            debugPrint('✅ تم رفع ${uploadedUrls.length} من ${_selectedImages.length} صورة بنجاح');
            debugPrint('🔗 الصورة الرئيسية: $imageUrl');
          }
        } else {
          throw Exception('فشل في رفع جميع الصور إلى Firebase Storage');
        }
      } else if (!_useImageUpload) {
        imageUrl = _imageUrlController.text.trim();
        imageUrls = imageUrl.isNotEmpty ? [imageUrl] : [];
      }

      // إنشاء منتج جديد
      final newProduct = Product(
        id: productId,
        name: _nameController.text.trim(),
        description: _descriptionController.text.trim(),
        price: double.parse(_priceController.text),
        originalPrice: double.parse(_priceController.text),
        image: imageUrl, // الصورة الرئيسية
        images: imageUrls, // جميع الصور
        categoryId: _selectedCategoryId!,
        categoryName: selectedCategory.name,
        rating: 0.0,
        reviewsCount: 0,
        inStock: int.parse(_stockController.text) > 0,
        stockQuantity: int.parse(_stockController.text),
        specifications: {},
        brand: selectedBrandName ?? _brandController.text.trim(),
        type: ProductType.eyeglasses, // افتراضي، يمكن تحسينه لاحقاً
        stock: int.parse(_stockController.text),
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        isFeatured: _isFeatured,
        tags: _skuController.text.trim().isEmpty
            ? null
            : _skuController.text.trim(),
      );

      // محاكاة حفظ المنتج
      await Future.delayed(const Duration(seconds: 1));

      // حفظ المنتج في Firebase والبيانات المحلية
      try {
        if (kDebugMode) {
          debugPrint('🔄 حفظ المنتج في Firebase...');
        }

        // استخدام Firebase Platform Service مع كائن Product
        final success = await FirebasePlatformService.addProduct(newProduct);

        if (success) {
          if (kDebugMode) {
            debugPrint('✅ تم حفظ المنتج في Firebase بنجاح');
          }
        } else {
          throw Exception('فشل في حفظ المنتج في Firebase');
        }
      } catch (e) {
        if (kDebugMode) {
          debugPrint('❌ خطأ في حفظ المنتج في Firebase: $e');
        }
        // في حالة فشل Firebase، احفظ محلياً
        try {
          await ApiService.addProduct(newProduct);
          if (kDebugMode) {
            debugPrint('📂 تم حفظ المنتج محلياً كبديل');
          }
        } catch (localError) {
          if (kDebugMode) {
            debugPrint('❌ فشل في الحفظ المحلي أيضاً: $localError');
          }
          throw Exception('فشل في حفظ المنتج');
        }
      }
      if (kDebugMode) {
        debugPrint('تم إنشاء منتج جديد: ${newProduct.name}');
      }

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم إضافة المنتج بنجاح - سيتم إعادة تحميل الصفحة'),
            backgroundColor: AppColors.successColor,
            duration: Duration(seconds: 2),
          ),
        );

        // انتظار قصير ثم إعادة تحميل الصفحة لجلب البيانات المحدثة
        await Future.delayed(const Duration(seconds: 2));

        // إعادة تحميل الصفحة تلقائياً
        if (mounted) {
          Navigator.of(context).pop();
        } else {
          if (mounted) {
            Navigator.pop(context, true); // إرجاع true للإشارة إلى نجاح الإضافة
          }
        }
      }
    } catch (e) {
      if (kDebugMode) {
        debugPrint('❌ خطأ في حفظ المنتج: $e');
      }

      // تحقق إذا كان الخطأ متعلق بإعادة التحميل (يمكن تجاهله)
      final errorString = e.toString().toLowerCase();
      final isReloadError =
          errorString.contains('reload') ||
          errorString.contains('location') ||
          errorString.contains('navigation');

      if (!isReloadError && mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('حدث خطأ أثناء حفظ المنتج'),
            backgroundColor: AppColors.errorColor,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }
}
