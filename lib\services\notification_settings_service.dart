import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';

/// خدمة إدارة إعدادات الإشعارات
class NotificationSettingsService {
  static const String _keyPrefix = 'notification_settings_';
  static const String _keyOrderNotifications = '${_keyPrefix}order_notifications';
  static const String _keyPromotionNotifications = '${_keyPrefix}promotion_notifications';
  static const String _keyNewsNotifications = '${_keyPrefix}news_notifications';
  static const String _keyReminderNotifications = '${_keyPrefix}reminder_notifications';
  static const String _keySoundEnabled = '${_keyPrefix}sound_enabled';
  static const String _keyVibrationEnabled = '${_keyPrefix}vibration_enabled';
  static const String _keySelectedSound = '${_keyPrefix}selected_sound';
  static const String _keyQuietHoursEnabled = '${_keyPrefix}quiet_hours_enabled';
  static const String _keyQuietHoursStart = '${_keyPrefix}quiet_hours_start';
  static const String _keyQuietHoursEnd = '${_keyPrefix}quiet_hours_end';

  static SharedPreferences? _prefs;

  /// تهيئة الخدمة
  static Future<void> initialize() async {
    try {
      _prefs = await SharedPreferences.getInstance();
      if (kDebugMode) {
        print('✅ تم تهيئة خدمة إعدادات الإشعارات');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في تهيئة خدمة إعدادات الإشعارات: $e');
      }
    }
  }

  /// التأكد من تهيئة الخدمة
  static Future<SharedPreferences> _getPrefs() async {
    if (_prefs == null) {
      await initialize();
    }
    return _prefs!;
  }

  // === إعدادات أنواع الإشعارات ===

  /// الحصول على حالة إشعارات الطلبات
  static Future<bool> getOrderNotifications() async {
    final prefs = await _getPrefs();
    return prefs.getBool(_keyOrderNotifications) ?? true; // افتراضي: مفعل
  }

  /// تعيين حالة إشعارات الطلبات
  static Future<void> setOrderNotifications(bool enabled) async {
    final prefs = await _getPrefs();
    await prefs.setBool(_keyOrderNotifications, enabled);
    if (kDebugMode) {
      print('📱 تم ${enabled ? 'تفعيل' : 'إلغاء'} إشعارات الطلبات');
    }
  }

  /// الحصول على حالة إشعارات العروض
  static Future<bool> getPromotionNotifications() async {
    final prefs = await _getPrefs();
    return prefs.getBool(_keyPromotionNotifications) ?? true;
  }

  /// تعيين حالة إشعارات العروض
  static Future<void> setPromotionNotifications(bool enabled) async {
    final prefs = await _getPrefs();
    await prefs.setBool(_keyPromotionNotifications, enabled);
    if (kDebugMode) {
      print('🎉 تم ${enabled ? 'تفعيل' : 'إلغاء'} إشعارات العروض');
    }
  }

  /// الحصول على حالة إشعارات الأخبار
  static Future<bool> getNewsNotifications() async {
    final prefs = await _getPrefs();
    return prefs.getBool(_keyNewsNotifications) ?? false; // افتراضي: معطل
  }

  /// تعيين حالة إشعارات الأخبار
  static Future<void> setNewsNotifications(bool enabled) async {
    final prefs = await _getPrefs();
    await prefs.setBool(_keyNewsNotifications, enabled);
    if (kDebugMode) {
      print('📰 تم ${enabled ? 'تفعيل' : 'إلغاء'} إشعارات الأخبار');
    }
  }

  /// الحصول على حالة إشعارات التذكيرات
  static Future<bool> getReminderNotifications() async {
    final prefs = await _getPrefs();
    return prefs.getBool(_keyReminderNotifications) ?? true;
  }

  /// تعيين حالة إشعارات التذكيرات
  static Future<void> setReminderNotifications(bool enabled) async {
    final prefs = await _getPrefs();
    await prefs.setBool(_keyReminderNotifications, enabled);
    if (kDebugMode) {
      print('⏰ تم ${enabled ? 'تفعيل' : 'إلغاء'} إشعارات التذكيرات');
    }
  }

  // === إعدادات الصوت والاهتزاز ===

  /// الحصول على حالة تفعيل الصوت
  static Future<bool> getSoundEnabled() async {
    final prefs = await _getPrefs();
    return prefs.getBool(_keySoundEnabled) ?? true;
  }

  /// تعيين حالة تفعيل الصوت
  static Future<void> setSoundEnabled(bool enabled) async {
    final prefs = await _getPrefs();
    await prefs.setBool(_keySoundEnabled, enabled);
    if (kDebugMode) {
      print('🔊 تم ${enabled ? 'تفعيل' : 'إلغاء'} صوت الإشعارات');
    }
  }

  /// الحصول على حالة تفعيل الاهتزاز
  static Future<bool> getVibrationEnabled() async {
    final prefs = await _getPrefs();
    return prefs.getBool(_keyVibrationEnabled) ?? true;
  }

  /// تعيين حالة تفعيل الاهتزاز
  static Future<void> setVibrationEnabled(bool enabled) async {
    final prefs = await _getPrefs();
    await prefs.setBool(_keyVibrationEnabled, enabled);
    if (kDebugMode) {
      print('📳 تم ${enabled ? 'تفعيل' : 'إلغاء'} اهتزاز الإشعارات');
    }
  }

  /// الحصول على النغمة المختارة
  static Future<String> getSelectedSound() async {
    final prefs = await _getPrefs();
    return prefs.getString(_keySelectedSound) ?? 'افتراضي';
  }

  /// تعيين النغمة المختارة
  static Future<void> setSelectedSound(String sound) async {
    final prefs = await _getPrefs();
    await prefs.setString(_keySelectedSound, sound);
    if (kDebugMode) {
      print('🎵 تم تعيين نغمة الإشعار: $sound');
    }
  }

  // === إعدادات الساعات الهادئة ===

  /// الحصول على حالة تفعيل الساعات الهادئة
  static Future<bool> getQuietHoursEnabled() async {
    final prefs = await _getPrefs();
    return prefs.getBool(_keyQuietHoursEnabled) ?? false;
  }

  /// تعيين حالة تفعيل الساعات الهادئة
  static Future<void> setQuietHoursEnabled(bool enabled) async {
    final prefs = await _getPrefs();
    await prefs.setBool(_keyQuietHoursEnabled, enabled);
    if (kDebugMode) {
      print('🌙 تم ${enabled ? 'تفعيل' : 'إلغاء'} الساعات الهادئة');
    }
  }

  /// الحصول على وقت بداية الساعات الهادئة
  static Future<Map<String, int>> getQuietHoursStart() async {
    final prefs = await _getPrefs();
    final jsonString = prefs.getString(_keyQuietHoursStart);
    if (jsonString != null) {
      final Map<String, dynamic> data = json.decode(jsonString);
      return {
        'hour': data['hour'] ?? 22,
        'minute': data['minute'] ?? 0,
      };
    }
    return {'hour': 22, 'minute': 0}; // افتراضي: 10:00 مساءً
  }

  /// تعيين وقت بداية الساعات الهادئة
  static Future<void> setQuietHoursStart(int hour, int minute) async {
    final prefs = await _getPrefs();
    final data = {'hour': hour, 'minute': minute};
    await prefs.setString(_keyQuietHoursStart, json.encode(data));
    if (kDebugMode) {
      print('🌙 تم تعيين بداية الساعات الهادئة: ${hour.toString().padLeft(2, '0')}:${minute.toString().padLeft(2, '0')}');
    }
  }

  /// الحصول على وقت نهاية الساعات الهادئة
  static Future<Map<String, int>> getQuietHoursEnd() async {
    final prefs = await _getPrefs();
    final jsonString = prefs.getString(_keyQuietHoursEnd);
    if (jsonString != null) {
      final Map<String, dynamic> data = json.decode(jsonString);
      return {
        'hour': data['hour'] ?? 8,
        'minute': data['minute'] ?? 0,
      };
    }
    return {'hour': 8, 'minute': 0}; // افتراضي: 8:00 صباحاً
  }

  /// تعيين وقت نهاية الساعات الهادئة
  static Future<void> setQuietHoursEnd(int hour, int minute) async {
    final prefs = await _getPrefs();
    final data = {'hour': hour, 'minute': minute};
    await prefs.setString(_keyQuietHoursEnd, json.encode(data));
    if (kDebugMode) {
      print('🌅 تم تعيين نهاية الساعات الهادئة: ${hour.toString().padLeft(2, '0')}:${minute.toString().padLeft(2, '0')}');
    }
  }

  // === دوال مساعدة ===

  /// التحقق من أن الوقت الحالي في الساعات الهادئة
  static Future<bool> isInQuietHours() async {
    final enabled = await getQuietHoursEnabled();
    if (!enabled) return false;

    final now = DateTime.now();
    final currentMinutes = now.hour * 60 + now.minute;

    final start = await getQuietHoursStart();
    final end = await getQuietHoursEnd();

    final startMinutes = start['hour']! * 60 + start['minute']!;
    final endMinutes = end['hour']! * 60 + end['minute']!;

    // إذا كانت الساعات الهادئة تمتد عبر منتصف الليل
    if (startMinutes > endMinutes) {
      return currentMinutes >= startMinutes || currentMinutes <= endMinutes;
    } else {
      return currentMinutes >= startMinutes && currentMinutes <= endMinutes;
    }
  }

  /// الحصول على جميع الإعدادات
  static Future<Map<String, dynamic>> getAllSettings() async {
    return {
      'orderNotifications': await getOrderNotifications(),
      'promotionNotifications': await getPromotionNotifications(),
      'newsNotifications': await getNewsNotifications(),
      'reminderNotifications': await getReminderNotifications(),
      'soundEnabled': await getSoundEnabled(),
      'vibrationEnabled': await getVibrationEnabled(),
      'selectedSound': await getSelectedSound(),
      'quietHoursEnabled': await getQuietHoursEnabled(),
      'quietHoursStart': await getQuietHoursStart(),
      'quietHoursEnd': await getQuietHoursEnd(),
    };
  }

  /// إعادة تعيين جميع الإعدادات للافتراضي
  static Future<void> resetToDefaults() async {
    final prefs = await _getPrefs();
    
    // حذف جميع المفاتيح المتعلقة بالإشعارات
    final keys = [
      _keyOrderNotifications,
      _keyPromotionNotifications,
      _keyNewsNotifications,
      _keyReminderNotifications,
      _keySoundEnabled,
      _keyVibrationEnabled,
      _keySelectedSound,
      _keyQuietHoursEnabled,
      _keyQuietHoursStart,
      _keyQuietHoursEnd,
    ];

    for (final key in keys) {
      await prefs.remove(key);
    }

    if (kDebugMode) {
      print('🔄 تم إعادة تعيين جميع إعدادات الإشعارات للافتراضي');
    }
  }

  /// التحقق من إمكانية إرسال نوع معين من الإشعارات
  static Future<bool> canSendNotification(String type) async {
    // التحقق من الساعات الهادئة أولاً
    if (await isInQuietHours()) {
      if (kDebugMode) {
        print('🌙 تم منع الإشعار بسبب الساعات الهادئة');
      }
      return false;
    }

    // التحقق من نوع الإشعار
    switch (type.toLowerCase()) {
      case 'order':
        return await getOrderNotifications();
      case 'promotion':
        return await getPromotionNotifications();
      case 'news':
        return await getNewsNotifications();
      case 'reminder':
        return await getReminderNotifications();
      default:
        return true; // افتراضي: السماح بالإشعارات غير المصنفة
    }
  }
}
