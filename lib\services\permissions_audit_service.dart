import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:permission_handler/permission_handler.dart';
import 'secure_auth_service.dart';
import 'jwt_service.dart';

/// خدمة مراجعة وتدقيق الأذونات
class PermissionsAuditService {
  static const String _auditLogKey = 'permissions_audit_log';
  static const String _permissionSettingsKey = 'permission_settings';
  
  /// الأذونات المطلوبة للتطبيق
  static final Map<Permission, String> _requiredPermissions = {
    Permission.camera: 'الكاميرا - لالتقاط صور المنتجات',
    Permission.storage: 'التخزين - لحفظ الصور والبيانات',
    Permission.location: 'الموقع - لتحديد المتاجر القريبة',
    Permission.notification: 'الإشعارات - لتنبيهات الطلبات',
    Permission.phone: 'الهاتف - للاتصال بالدعم',
  };
  
  /// فحص جميع الأذونات المطلوبة
  static Future<PermissionAuditResult> auditAllPermissions() async {
    try {
      if (kDebugMode) {
        print('🔍 بدء مراجعة شاملة للأذونات...');
      }
      
      final results = <Permission, PermissionStatus>{};
      final issues = <String>[];
      final recommendations = <String>[];
      
      // فحص كل إذن مطلوب
      for (final permission in _requiredPermissions.keys) {
        try {
          final status = await permission.status;
          results[permission] = status;
          
          // تحليل حالة الإذن
          switch (status) {
            case PermissionStatus.denied:
              issues.add('إذن ${_requiredPermissions[permission]} مرفوض');
              recommendations.add('اطلب إذن ${_requiredPermissions[permission]}');
              break;
            case PermissionStatus.permanentlyDenied:
              issues.add('إذن ${_requiredPermissions[permission]} مرفوض نهائياً');
              recommendations.add('اذهب إلى الإعدادات لتفعيل ${_requiredPermissions[permission]}');
              break;
            case PermissionStatus.restricted:
              issues.add('إذن ${_requiredPermissions[permission]} مقيد');
              recommendations.add('تحقق من قيود النظام لـ ${_requiredPermissions[permission]}');
              break;
            case PermissionStatus.limited:
              recommendations.add('إذن ${_requiredPermissions[permission]} محدود - فكر في طلب إذن كامل');
              break;
            case PermissionStatus.granted:
              // لا مشاكل
              break;
            case PermissionStatus.provisional:
              recommendations.add('إذن ${_requiredPermissions[permission]} مؤقت - قد يحتاج تأكيد');
              break;
          }
          
          if (kDebugMode) {
            print('📋 ${_requiredPermissions[permission]}: ${status.name}');
          }
        } catch (e) {
          issues.add('خطأ في فحص إذن ${_requiredPermissions[permission]}: $e');
        }
      }
      
      // حساب النتيجة الإجمالية
      final grantedCount = results.values.where((status) => 
        status == PermissionStatus.granted || status == PermissionStatus.limited
      ).length;
      
      final totalCount = results.length;
      final complianceScore = totalCount > 0 ? (grantedCount / totalCount * 100) : 0.0;
      
      final auditResult = PermissionAuditResult(
        permissions: results,
        issues: issues,
        recommendations: recommendations,
        complianceScore: complianceScore,
        auditDate: DateTime.now(),
      );
      
      // حفظ نتيجة المراجعة
      await _saveAuditResult(auditResult);
      
      if (kDebugMode) {
        print('✅ تم إكمال مراجعة الأذونات');
        print('📊 نقاط الامتثال: ${complianceScore.toStringAsFixed(1)}%');
        print('⚠️ المشاكل: ${issues.length}');
        print('💡 التوصيات: ${recommendations.length}');
      }
      
      return auditResult;
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في مراجعة الأذونات: $e');
      }
      
      return PermissionAuditResult(
        permissions: {},
        issues: ['خطأ في مراجعة الأذونات: $e'],
        recommendations: ['أعد المحاولة لاحقاً'],
        complianceScore: 0.0,
        auditDate: DateTime.now(),
      );
    }
  }
  
  /// طلب الأذونات المفقودة
  static Future<bool> requestMissingPermissions() async {
    try {
      if (kDebugMode) {
        print('📱 طلب الأذونات المفقودة...');
      }
      
      final missingPermissions = <Permission>[];
      
      // فحص الأذونات المفقودة
      for (final permission in _requiredPermissions.keys) {
        final status = await permission.status;
        if (status == PermissionStatus.denied) {
          missingPermissions.add(permission);
        }
      }
      
      if (missingPermissions.isEmpty) {
        if (kDebugMode) {
          print('✅ جميع الأذونات ممنوحة');
        }
        return true;
      }
      
      // طلب الأذونات المفقودة
      final results = await missingPermissions.request();
      
      // فحص النتائج
      bool allGranted = true;
      for (final entry in results.entries) {
        final permission = entry.key;
        final status = entry.value;
        
        if (status != PermissionStatus.granted && status != PermissionStatus.limited) {
          allGranted = false;
          
          if (kDebugMode) {
            print('❌ لم يتم منح إذن ${_requiredPermissions[permission]}: ${status.name}');
          }
        } else {
          if (kDebugMode) {
            print('✅ تم منح إذن ${_requiredPermissions[permission]}');
          }
        }
      }
      
      // تسجيل النتيجة
      await _logPermissionRequest(results);
      
      return allGranted;
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في طلب الأذونات: $e');
      }
      return false;
    }
  }
  
  /// فحص أذونات التطبيق
  static Future<AppPermissionStatus> checkAppPermissions() async {
    try {
      final currentUser = SecureAuthService.currentUser;
      final accessToken = SecureAuthService.accessToken;
      
      // فحص أذونات المستخدم في التطبيق
      final userPermissions = <String>[];
      
      if (accessToken != null) {
        final tokenPermissions = JWTService.getPermissionsFromToken(accessToken);
        if (tokenPermissions != null) {
          userPermissions.addAll(tokenPermissions);
        }
      }
      
      // فحص الأذونات الإدارية
      final isAdmin = SecureAuthService.isAdmin;
      
      // فحص أذونات النظام
      final systemPermissions = await auditAllPermissions();
      
      return AppPermissionStatus(
        userId: currentUser?.id,
        userEmail: currentUser?.email,
        isAdmin: isAdmin,
        userPermissions: userPermissions,
        systemPermissionAudit: systemPermissions,
        checkedAt: DateTime.now(),
      );
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في فحص أذونات التطبيق: $e');
      }
      
      return AppPermissionStatus(
        userId: null,
        userEmail: null,
        isAdmin: false,
        userPermissions: [],
        systemPermissionAudit: PermissionAuditResult(
          permissions: {},
          issues: ['خطأ في فحص الأذونات'],
          recommendations: [],
          complianceScore: 0.0,
          auditDate: DateTime.now(),
        ),
        checkedAt: DateTime.now(),
      );
    }
  }
  
  /// حفظ نتيجة المراجعة
  static Future<void> _saveAuditResult(PermissionAuditResult result) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final existingLogs = prefs.getStringList(_auditLogKey) ?? [];
      
      existingLogs.add(jsonEncode(result.toJson()));
      
      // الاحتفاظ بآخر 50 مراجعة فقط
      if (existingLogs.length > 50) {
        existingLogs.removeRange(0, existingLogs.length - 50);
      }
      
      await prefs.setStringList(_auditLogKey, existingLogs);
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في حفظ نتيجة المراجعة: $e');
      }
    }
  }
  
  /// تسجيل طلب الأذونات
  static Future<void> _logPermissionRequest(Map<Permission, PermissionStatus> results) async {
    try {
      final logEntry = {
        'timestamp': DateTime.now().toIso8601String(),
        'type': 'permission_request',
        'userId': SecureAuthService.currentUser?.id,
        'results': results.map((permission, status) => MapEntry(
          _requiredPermissions[permission] ?? permission.toString(),
          status.name,
        )),
      };
      
      final prefs = await SharedPreferences.getInstance();
      final existingLogs = prefs.getStringList(_auditLogKey) ?? [];
      existingLogs.add(jsonEncode(logEntry));
      
      if (existingLogs.length > 100) {
        existingLogs.removeRange(0, existingLogs.length - 100);
      }
      
      await prefs.setStringList(_auditLogKey, existingLogs);
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في تسجيل طلب الأذونات: $e');
      }
    }
  }
  
  /// الحصول على سجل المراجعات
  static Future<List<Map<String, dynamic>>> getAuditHistory() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final logsStrings = prefs.getStringList(_auditLogKey) ?? [];
      
      return logsStrings
          .map((logString) => jsonDecode(logString) as Map<String, dynamic>)
          .toList()
          .reversed
          .toList();
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في جلب سجل المراجعات: $e');
      }
      return [];
    }
  }
}

/// نتيجة مراجعة الأذونات
class PermissionAuditResult {
  final Map<Permission, PermissionStatus> permissions;
  final List<String> issues;
  final List<String> recommendations;
  final double complianceScore;
  final DateTime auditDate;
  
  const PermissionAuditResult({
    required this.permissions,
    required this.issues,
    required this.recommendations,
    required this.complianceScore,
    required this.auditDate,
  });
  
  Map<String, dynamic> toJson() {
    return {
      'permissions': permissions.map((permission, status) => MapEntry(
        permission.toString(),
        status.name,
      )),
      'issues': issues,
      'recommendations': recommendations,
      'complianceScore': complianceScore,
      'auditDate': auditDate.toIso8601String(),
    };
  }
  
  bool get hasIssues => issues.isNotEmpty;
  bool get isCompliant => complianceScore >= 80.0;
  
  @override
  String toString() {
    return 'PermissionAuditResult(score: ${complianceScore.toStringAsFixed(1)}%, issues: ${issues.length})';
  }
}

/// حالة أذونات التطبيق
class AppPermissionStatus {
  final String? userId;
  final String? userEmail;
  final bool isAdmin;
  final List<String> userPermissions;
  final PermissionAuditResult systemPermissionAudit;
  final DateTime checkedAt;
  
  const AppPermissionStatus({
    required this.userId,
    required this.userEmail,
    required this.isAdmin,
    required this.userPermissions,
    required this.systemPermissionAudit,
    required this.checkedAt,
  });
  
  Map<String, dynamic> toJson() {
    return {
      'userId': userId,
      'userEmail': userEmail,
      'isAdmin': isAdmin,
      'userPermissions': userPermissions,
      'systemPermissionAudit': systemPermissionAudit.toJson(),
      'checkedAt': checkedAt.toIso8601String(),
    };
  }
  
  bool get hasSystemIssues => systemPermissionAudit.hasIssues;
  bool get isSystemCompliant => systemPermissionAudit.isCompliant;
  
  @override
  String toString() {
    return 'AppPermissionStatus(user: $userEmail, admin: $isAdmin, systemCompliant: $isSystemCompliant)';
  }
}
