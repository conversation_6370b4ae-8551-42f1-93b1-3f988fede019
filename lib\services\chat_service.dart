import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/foundation.dart';
import '../models/chat_message.dart';
import '../models/user_simple.dart';

/// استثناء خدمة الدردشة
class ChatServiceException implements Exception {
  final String message;
  final String? code;

  const ChatServiceException(this.message, [this.code]);

  @override
  String toString() => 'ChatServiceException: $message${code != null ? ' (Code: $code)' : ''}';
}

/// خدمة إدارة الدردشة
class ChatService {
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  // مراجع المجموعات
  static final CollectionReference _conversationsRef = _firestore.collection('conversations');
  static final CollectionReference _messagesRef = _firestore.collection('messages');

  /// التحقق من حالة الاتصال بـ Firestore
  static Future<bool> _checkFirestoreConnection() async {
    try {
      // محاولة بسيطة للاتصال بـ Firestore
      await _firestore.enableNetwork();
      return true;
    } catch (e) {
      if (kDebugMode) {
        print('❌ فشل الاتصال بـ Firestore: $e');
      }
      return false;
    }
  }

  /// إنشاء محادثة جديدة أو الحصول على المحادثة الموجودة
  static Future<String> getOrCreateConversation(User customer) async {
    try {
      if (kDebugMode) {
        print('🔍 البحث عن محادثة للعميل: ${customer.email}');
      }

      // التحقق من الاتصال أولاً
      final isConnected = await _checkFirestoreConnection();
      if (!isConnected) {
        throw const ChatServiceException(
          'لا يمكن الاتصال بخدمة الدردشة. تحقق من اتصال الإنترنت.',
          'CONNECTION_FAILED'
        );
      }

      // البحث عن محادثة موجودة للعميل
      final existingConversation = await _conversationsRef
          .where('customerId', isEqualTo: customer.id)
          .where('isActive', isEqualTo: true)
          .limit(1)
          .get()
          .timeout(
            const Duration(seconds: 10),
            onTimeout: () {
              throw const ChatServiceException(
                'انتهت مهلة البحث عن المحادثة',
                'TIMEOUT'
              );
            },
          );

      if (existingConversation.docs.isNotEmpty) {
        final conversationId = existingConversation.docs.first.id;
        if (kDebugMode) {
          print('✅ تم العثور على محادثة موجودة: $conversationId');
        }
        return conversationId;
      }

      // إنشاء محادثة جديدة
      final newConversation = Conversation(
        id: '', // سيتم تعيينه تلقائياً
        customerId: customer.id,
        customerName: '${customer.firstName} ${customer.lastName}',
        customerEmail: customer.email,
        createdAt: DateTime.now(),
        lastMessageAt: DateTime.now(),
        lastMessage: 'بدء المحادثة',
        lastMessageSender: 'system',
        unreadCount: 0,
        isActive: true,
        status: 'open',
      );

      final docRef = await _conversationsRef.add(newConversation.toFirestore());
      
      if (kDebugMode) {
        print('✅ تم إنشاء محادثة جديدة: ${docRef.id}');
      }

      return docRef.id;
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في إنشاء/الحصول على المحادثة: $e');
      }

      if (e is ChatServiceException) {
        rethrow;
      }

      // تحويل أخطاء Firebase إلى أخطاء مفهومة
      if (e.toString().contains('permission-denied')) {
        throw const ChatServiceException(
          'ليس لديك صلاحية للوصول إلى الدردشة',
          'PERMISSION_DENIED'
        );
      } else if (e.toString().contains('unavailable')) {
        throw const ChatServiceException(
          'خدمة الدردشة غير متاحة حالياً',
          'SERVICE_UNAVAILABLE'
        );
      } else {
        throw ChatServiceException(
          'خطأ غير متوقع في خدمة الدردشة: ${e.toString()}',
          'UNKNOWN_ERROR'
        );
      }
    }
  }

  /// إرسال رسالة
  static Future<void> sendMessage({
    required String conversationId,
    required String senderId,
    required String senderName,
    required String senderType,
    required String message,
    String? attachmentUrl,
    String? attachmentType,
  }) async {
    try {
      if (kDebugMode) {
        print('📤 إرسال رسالة في المحادثة: $conversationId');
      }

      final chatMessage = ChatMessage(
        id: '', // سيتم تعيينه تلقائياً
        conversationId: conversationId,
        senderId: senderId,
        senderName: senderName,
        senderType: senderType,
        message: message,
        timestamp: DateTime.now(),
        isRead: false,
        attachmentUrl: attachmentUrl,
        attachmentType: attachmentType,
      );

      // إضافة الرسالة
      await _messagesRef.add(chatMessage.toFirestore());

      // تحديث المحادثة
      await _updateConversationLastMessage(
        conversationId: conversationId,
        lastMessage: message,
        lastMessageSender: senderType,
        incrementUnread: senderType == 'customer',
      );

      if (kDebugMode) {
        print('✅ تم إرسال الرسالة بنجاح');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في إرسال الرسالة: $e');
      }
      rethrow;
    }
  }

  /// الحصول على رسائل المحادثة (مبسط بدون فهارس معقدة)
  static Stream<List<ChatMessage>> getMessages(String conversationId) {
    try {
      if (kDebugMode) {
        print('📥 الاستماع لرسائل المحادثة: $conversationId');
      }

      // استعلام مبسط بدون orderBy لتجنب مشكلة الفهارس
      return _messagesRef
          .where('conversationId', isEqualTo: conversationId)
          .snapshots()
          .map((snapshot) {
        final messages = snapshot.docs
            .map((doc) => ChatMessage.fromFirestore(doc))
            .toList();

        // ترتيب الرسائل في الكود بدلاً من Firestore
        messages.sort((a, b) => a.timestamp.compareTo(b.timestamp));

        return messages;
      });
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في الحصول على الرسائل: $e');
      }
      return Stream.error(e);
    }
  }

  /// الحصول على جميع المحادثات (للإدارة) - مبسط بدون فهارس
  static Stream<List<Conversation>> getAllConversations() {
    try {
      if (kDebugMode) {
        print('📋 الحصول على جميع المحادثات');
      }

      // استعلام مبسط بدون orderBy لتجنب مشكلة الفهارس
      return _conversationsRef
          .where('isActive', isEqualTo: true)
          .snapshots()
          .map((snapshot) {
        final conversations = snapshot.docs
            .map((doc) => Conversation.fromFirestore(doc))
            .toList();

        // ترتيب المحادثات في الكود بدلاً من Firestore
        conversations.sort((a, b) => b.lastMessageAt.compareTo(a.lastMessageAt));

        return conversations;
      });
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في الحصول على المحادثات: $e');
      }
      return Stream.error(e);
    }
  }

  /// تحديث حالة قراءة الرسائل (مبسط)
  static Future<void> markMessagesAsRead(String conversationId, String userId) async {
    try {
      if (kDebugMode) {
        print('👁️ تحديد الرسائل كمقروءة للمحادثة: $conversationId');
      }

      // استعلام مبسط بدون فهارس معقدة
      final allMessages = await _messagesRef
          .where('conversationId', isEqualTo: conversationId)
          .get();

      // فلترة الرسائل في الكود بدلاً من Firestore
      final unreadMessages = allMessages.docs.where((doc) {
        final data = doc.data() as Map<String, dynamic>;
        return data['senderId'] != userId && data['isRead'] == false;
      }).toList();

      final batch = _firestore.batch();

      for (final doc in unreadMessages) {
        batch.update(doc.reference, {'isRead': true});
      }

      await batch.commit();

      // إعادة تعيين عداد الرسائل غير المقروءة في المحادثة
      await _conversationsRef.doc(conversationId).update({
        'unreadCount': 0,
      });

      if (kDebugMode) {
        print('✅ تم تحديد ${unreadMessages.length} رسالة كمقروءة');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في تحديد الرسائل كمقروءة: $e');
      }
      rethrow;
    }
  }

  /// تحديث آخر رسالة في المحادثة
  static Future<void> _updateConversationLastMessage({
    required String conversationId,
    required String lastMessage,
    required String lastMessageSender,
    bool incrementUnread = false,
  }) async {
    try {
      final updateData = {
        'lastMessage': lastMessage,
        'lastMessageSender': lastMessageSender,
        'lastMessageAt': Timestamp.now(),
      };

      if (incrementUnread) {
        updateData['unreadCount'] = FieldValue.increment(1);
      }

      await _conversationsRef.doc(conversationId).update(updateData);
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في تحديث آخر رسالة: $e');
      }
      rethrow;
    }
  }

  /// إغلاق المحادثة
  static Future<void> closeConversation(String conversationId) async {
    try {
      await _conversationsRef.doc(conversationId).update({
        'status': 'closed',
        'isActive': false,
      });

      if (kDebugMode) {
        print('✅ تم إغلاق المحادثة: $conversationId');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في إغلاق المحادثة: $e');
      }
      rethrow;
    }
  }

  /// إعادة فتح المحادثة
  static Future<void> reopenConversation(String conversationId) async {
    try {
      await _conversationsRef.doc(conversationId).update({
        'status': 'open',
        'isActive': true,
      });

      if (kDebugMode) {
        print('✅ تم إعادة فتح المحادثة: $conversationId');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في إعادة فتح المحادثة: $e');
      }
      rethrow;
    }
  }

  /// الحصول على عدد الرسائل غير المقروءة للإدارة - مبسط
  static Stream<int> getUnreadMessagesCount() {
    try {
      // استعلام مبسط بدون where متعددة لتجنب مشكلة الفهارس
      return _conversationsRef
          .where('isActive', isEqualTo: true)
          .snapshots()
          .map((snapshot) {
        int totalUnread = 0;
        for (final doc in snapshot.docs) {
          final data = doc.data() as Map<String, dynamic>;
          final unreadCount = data['unreadCount'] as int? ?? 0;
          if (unreadCount > 0) {
            totalUnread += unreadCount;
          }
        }
        return totalUnread;
      });
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في الحصول على عدد الرسائل غير المقروءة: $e');
      }
      return Stream.error(e);
    }
  }
}
