import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import '../../app_properties.dart';
import '../../services/secure_auth_service.dart';
import '../../services/secure_storage_service.dart';
import '../../services/encryption_service.dart';
import '../../services/jwt_service.dart';
import 'security_report_page.dart';

class SecuritySettingsPage extends StatefulWidget {
  const SecuritySettingsPage({super.key});

  @override
  State<SecuritySettingsPage> createState() => _SecuritySettingsPageState();
}

class _SecuritySettingsPageState extends State<SecuritySettingsPage> {
  bool _isLoading = true;
  Map<String, dynamic> _securityInfo = {};
  
  final _currentPasswordController = TextEditingController();
  final _newPasswordController = TextEditingController();
  final _confirmPasswordController = TextEditingController();
  
  bool _showCurrentPassword = false;
  bool _showNewPassword = false;
  bool _showConfirmPassword = false;

  @override
  void initState() {
    super.initState();
    _loadSecurityInfo();
  }

  @override
  void dispose() {
    _currentPasswordController.dispose();
    _newPasswordController.dispose();
    _confirmPasswordController.dispose();
    super.dispose();
  }

  Future<void> _loadSecurityInfo() async {
    try {
      final info = await SecureAuthService.getSecurityInfo();
      setState(() {
        _securityInfo = info;
        _isLoading = false;
      });
    } catch (e) {
      if (kDebugMode) {
        print('خطأ في تحميل معلومات الأمان: $e');
      }
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.scaffoldBackground,
      appBar: AppBar(
        title: const Text('إعدادات الأمان'),
        backgroundColor: AppColors.primaryColor,
        foregroundColor: AppColors.white,
        elevation: 0,
        centerTitle: true,
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : RefreshIndicator(
              onRefresh: _loadSecurityInfo,
              child: SingleChildScrollView(
                physics: const AlwaysScrollableScrollPhysics(),
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // معلومات الأمان العامة
                    _buildSecurityOverview(),
                    
                    const SizedBox(height: 24),
                    
                    // تغيير كلمة المرور
                    _buildPasswordSection(),
                    
                    const SizedBox(height: 24),
                    
                    // معلومات الجلسة
                    _buildSessionInfo(),
                    
                    const SizedBox(height: 24),
                    
                    // إحصائيات الأمان
                    _buildSecurityStats(),
                    
                    const SizedBox(height: 24),
                    
                    // إجراءات الأمان
                    _buildSecurityActions(),
                  ],
                ),
              ),
            ),
    );
  }

  Widget _buildSecurityOverview() {
    final isAuthenticated = _securityInfo['isAuthenticated'] ?? false;
    final isAdmin = _securityInfo['isAdmin'] ?? false;
    
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  isAuthenticated ? Icons.security : Icons.warning,
                  color: isAuthenticated ? AppColors.accentColor : AppColors.errorColor,
                ),
                const SizedBox(width: 8),
                Text(
                  'حالة الأمان',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 16),
            
            _buildStatusRow(
              'حالة المصادقة',
              isAuthenticated ? 'مصادق عليه' : 'غير مصادق',
              isAuthenticated ? AppColors.accentColor : AppColors.errorColor,
            ),
            
            _buildStatusRow(
              'نوع الحساب',
              isAdmin ? 'إداري' : 'مستخدم عادي',
              isAdmin ? AppColors.warning : AppColors.primaryColor,
            ),
            
            _buildStatusRow(
              'التشفير',
              'مفعل',
              AppColors.success,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatusRow(String label, String value, Color color) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(label),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: color.withOpacity(0.1),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: color.withOpacity(0.3)),
            ),
            child: Text(
              value,
              style: TextStyle(
                color: color,
                fontWeight: FontWeight.bold,
                fontSize: 12,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPasswordSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.lock, color: AppColors.primaryColor),
                const SizedBox(width: 8),
                Text(
                  'تغيير كلمة المرور',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 16),
            
            // كلمة المرور الحالية
            TextFormField(
              controller: _currentPasswordController,
              obscureText: !_showCurrentPassword,
              decoration: InputDecoration(
                labelText: 'كلمة المرور الحالية',
                prefixIcon: const Icon(Icons.lock_outline),
                suffixIcon: IconButton(
                  icon: Icon(
                    _showCurrentPassword ? Icons.visibility_off : Icons.visibility,
                  ),
                  onPressed: () {
                    setState(() {
                      _showCurrentPassword = !_showCurrentPassword;
                    });
                  },
                ),
                border: const OutlineInputBorder(),
              ),
            ),
            
            const SizedBox(height: 16),
            
            // كلمة المرور الجديدة
            TextFormField(
              controller: _newPasswordController,
              obscureText: !_showNewPassword,
              decoration: InputDecoration(
                labelText: 'كلمة المرور الجديدة',
                prefixIcon: const Icon(Icons.lock),
                suffixIcon: IconButton(
                  icon: Icon(
                    _showNewPassword ? Icons.visibility_off : Icons.visibility,
                  ),
                  onPressed: () {
                    setState(() {
                      _showNewPassword = !_showNewPassword;
                    });
                  },
                ),
                border: const OutlineInputBorder(),
                helperText: 'يجب أن تحتوي على 8 أحرف، حرف كبير، صغير، رقم، ورمز خاص',
              ),
              onChanged: (value) {
                setState(() {}); // لتحديث مؤشر قوة كلمة المرور
              },
            ),
            
            // مؤشر قوة كلمة المرور
            if (_newPasswordController.text.isNotEmpty) ...[
              const SizedBox(height: 8),
              _buildPasswordStrengthIndicator(_newPasswordController.text),
            ],
            
            const SizedBox(height: 16),
            
            // تأكيد كلمة المرور
            TextFormField(
              controller: _confirmPasswordController,
              obscureText: !_showConfirmPassword,
              decoration: InputDecoration(
                labelText: 'تأكيد كلمة المرور الجديدة',
                prefixIcon: const Icon(Icons.lock_outline),
                suffixIcon: IconButton(
                  icon: Icon(
                    _showConfirmPassword ? Icons.visibility_off : Icons.visibility,
                  ),
                  onPressed: () {
                    setState(() {
                      _showConfirmPassword = !_showConfirmPassword;
                    });
                  },
                ),
                border: const OutlineInputBorder(),
              ),
            ),
            
            const SizedBox(height: 16),
            
            // زر تغيير كلمة المرور
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: _changePassword,
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.primaryColor,
                  foregroundColor: AppColors.white,
                ),
                child: const Text('تغيير كلمة المرور'),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPasswordStrengthIndicator(String password) {
    final isStrong = EncryptionService.isPasswordStrong(password);
    final strength = _calculatePasswordStrength(password);
    
    Color color;
    String text;
    
    if (strength < 0.3) {
      color = AppColors.errorColor;
      text = 'ضعيفة';
    } else if (strength < 0.7) {
      color = AppColors.warning;
      text = 'متوسطة';
    } else {
      color = AppColors.success;
      text = 'قوية';
    }
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'قوة كلمة المرور:',
              style: Theme.of(context).textTheme.bodySmall,
            ),
            Text(
              text,
              style: TextStyle(
                color: color,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
        const SizedBox(height: 4),
        LinearProgressIndicator(
          value: strength,
          backgroundColor: Colors.grey[300],
          valueColor: AlwaysStoppedAnimation<Color>(color),
        ),
      ],
    );
  }

  double _calculatePasswordStrength(String password) {
    double strength = 0.0;
    
    // طول كلمة المرور
    if (password.length >= 8) strength += 0.2;
    if (password.length >= 12) strength += 0.1;
    
    // أحرف كبيرة
    if (password.contains(RegExp(r'[A-Z]'))) strength += 0.2;
    
    // أحرف صغيرة
    if (password.contains(RegExp(r'[a-z]'))) strength += 0.2;
    
    // أرقام
    if (password.contains(RegExp(r'[0-9]'))) strength += 0.2;
    
    // رموز خاصة
    if (password.contains(RegExp(r'[!@#$%^&*(),.?":{}|<>]'))) strength += 0.2;
    
    return strength.clamp(0.0, 1.0);
  }

  Widget _buildSessionInfo() {
    final tokenInfo = _securityInfo['tokenInfo'] as Map<String, dynamic>?;
    
    if (tokenInfo == null) {
      return const SizedBox.shrink();
    }
    
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.access_time, color: AppColors.primaryColor),
                const SizedBox(width: 8),
                Text(
                  'معلومات الجلسة',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 16),
            
            if (tokenInfo['issuedAt'] != null)
              _buildInfoRow(
                'تاريخ تسجيل الدخول',
                _formatDateTime(tokenInfo['issuedAt']),
              ),
            
            if (tokenInfo['expiresAt'] != null)
              _buildInfoRow(
                'انتهاء صلاحية الجلسة',
                _formatDateTime(tokenInfo['expiresAt']),
              ),
            
            _buildInfoRow(
              'نوع الرمز المميز',
              tokenInfo['tokenType'] ?? 'غير محدد',
            ),
            
            _buildInfoRow(
              'حالة الرمز',
              tokenInfo['isExpired'] == true ? 'منتهي الصلاحية' : 'صالح',
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSecurityStats() {
    final securityStats = _securityInfo['securityStats'] as Map<String, dynamic>?;
    
    if (securityStats == null) {
      return const SizedBox.shrink();
    }
    
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.analytics, color: AppColors.primaryColor),
                const SizedBox(width: 8),
                Text(
                  'إحصائيات الأمان',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 16),
            
            _buildInfoRow(
              'البيانات المشفرة',
              '${securityStats['encryptedDataCount'] ?? 0} عنصر',
            ),
            
            _buildInfoRow(
              'البيانات غير المشفرة',
              '${securityStats['unencryptedDataCount'] ?? 0} عنصر',
            ),
            
            _buildInfoRow(
              'حالة التشفير',
              securityStats['isFullySecure'] == true ? 'آمن بالكامل' : 'يحتاج تحسين',
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSecurityActions() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.security, color: AppColors.primaryColor),
                const SizedBox(width: 8),
                Text(
                  'إجراءات الأمان',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 16),
            
            // تحديث البيانات المشفرة
            ListTile(
              leading: const Icon(Icons.refresh, color: AppColors.primaryColor),
              title: const Text('تحديث التشفير'),
              subtitle: const Text('ترحيل البيانات القديمة إلى النظام الآمن'),
              trailing: const Icon(Icons.arrow_forward_ios),
              onTap: _migrateOldData,
            ),
            
            const Divider(),
            
            // فحص سلامة البيانات
            ListTile(
              leading: const Icon(Icons.verified_user, color: AppColors.success),
              title: const Text('فحص سلامة البيانات'),
              subtitle: const Text('التحقق من سلامة البيانات المشفرة'),
              trailing: const Icon(Icons.arrow_forward_ios),
              onTap: _verifyDataIntegrity,
            ),

            const Divider(),

            // تقرير الأمان الشامل
            ListTile(
              leading: const Icon(Icons.assessment, color: AppColors.primaryColor),
              title: const Text('تقرير الأمان الشامل'),
              subtitle: const Text('عرض تقرير مفصل عن حالة الأمان'),
              trailing: const Icon(Icons.arrow_forward_ios),
              onTap: _navigateToSecurityReport,
            ),
            
            const Divider(),
            
            // مسح البيانات الآمنة
            ListTile(
              leading: const Icon(Icons.delete_forever, color: AppColors.errorColor),
              title: const Text('مسح جميع البيانات'),
              subtitle: const Text('حذف جميع البيانات المحفوظة محلياً'),
              trailing: const Icon(Icons.arrow_forward_ios),
              onTap: _clearAllData,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Expanded(
            flex: 2,
            child: Text(
              label,
              style: Theme.of(context).textTheme.bodyMedium,
            ),
          ),
          Expanded(
            flex: 3,
            child: Text(
              value,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                fontWeight: FontWeight.w500,
              ),
              textAlign: TextAlign.end,
            ),
          ),
        ],
      ),
    );
  }

  String _formatDateTime(DateTime dateTime) {
    return '${dateTime.day}/${dateTime.month}/${dateTime.year} ${dateTime.hour}:${dateTime.minute.toString().padLeft(2, '0')}';
  }

  Future<void> _changePassword() async {
    final currentPassword = _currentPasswordController.text.trim();
    final newPassword = _newPasswordController.text.trim();
    final confirmPassword = _confirmPasswordController.text.trim();

    if (currentPassword.isEmpty || newPassword.isEmpty || confirmPassword.isEmpty) {
      _showMessage('يرجى ملء جميع الحقول', isError: true);
      return;
    }

    if (newPassword != confirmPassword) {
      _showMessage('كلمة المرور الجديدة وتأكيدها غير متطابقين', isError: true);
      return;
    }

    if (!EncryptionService.isPasswordStrong(newPassword)) {
      _showMessage('كلمة المرور الجديدة ضعيفة', isError: true);
      return;
    }

    try {
      final success = await SecureAuthService.changePassword(
        currentPassword: currentPassword,
        newPassword: newPassword,
      );

      if (success) {
        _showMessage('تم تغيير كلمة المرور بنجاح');
        _currentPasswordController.clear();
        _newPasswordController.clear();
        _confirmPasswordController.clear();
      } else {
        _showMessage('فشل في تغيير كلمة المرور', isError: true);
      }
    } catch (e) {
      _showMessage('حدث خطأ أثناء تغيير كلمة المرور', isError: true);
    }
  }

  Future<void> _migrateOldData() async {
    try {
      await SecureStorageService.migrateOldData();
      _showMessage('تم ترحيل البيانات بنجاح');
      await _loadSecurityInfo();
    } catch (e) {
      _showMessage('فشل في ترحيل البيانات', isError: true);
    }
  }

  Future<void> _verifyDataIntegrity() async {
    try {
      final isValid = await SecureStorageService.verifyDataIntegrity();
      if (isValid) {
        _showMessage('جميع البيانات سليمة');
      } else {
        _showMessage('تم العثور على بيانات تالفة', isError: true);
      }
    } catch (e) {
      _showMessage('فشل في فحص سلامة البيانات', isError: true);
    }
  }

  Future<void> _clearAllData() async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد الحذف'),
        content: const Text('هل أنت متأكد من حذف جميع البيانات؟ هذا الإجراء لا يمكن التراجع عنه.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: TextButton.styleFrom(foregroundColor: AppColors.errorColor),
            child: const Text('حذف'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      try {
        await SecureStorageService.clearAllSecureData();
        _showMessage('تم حذف جميع البيانات');
        await _loadSecurityInfo();
      } catch (e) {
        _showMessage('فشل في حذف البيانات', isError: true);
      }
    }
  }

  void _navigateToSecurityReport() {
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => const SecurityReportPage()),
    );
  }

  void _showMessage(String message, {bool isError = false}) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: isError ? AppColors.errorColor : AppColors.accentColor,
      ),
    );
  }
}
