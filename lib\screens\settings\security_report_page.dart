import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import '../../app_properties.dart';
import '../../services/secure_auth_service.dart';
import '../../services/api_security_service.dart';
import '../../services/ssl_pinning_service.dart';
import '../../services/permissions_audit_service.dart';

class SecurityReportPage extends StatefulWidget {
  const SecurityReportPage({super.key});

  @override
  State<SecurityReportPage> createState() => _SecurityReportPageState();
}

class _SecurityReportPageState extends State<SecurityReportPage> {
  bool _isLoading = true;
  Map<String, dynamic> _securityReport = {};

  @override
  void initState() {
    super.initState();
    _generateSecurityReport();
  }

  Future<void> _generateSecurityReport() async {
    try {
      setState(() {
        _isLoading = true;
      });

      if (kDebugMode) {
        print('📊 توليد تقرير الأمان الشامل...');
      }

      // جمع معلومات الأمان من جميع الخدمات مع معالجة الأخطاء
      Map<String, dynamic> authInfo = {};
      Map<String, dynamic> apiStats = {};
      Map<String, dynamic> permissionStatus = {};
      Map<String, dynamic> firebaseSecurityCheck = {};

      try {
        authInfo = await SecureAuthService.getSecurityInfo();
      } catch (e) {
        authInfo = {'error': 'فشل في جلب معلومات المصادقة: $e'};
      }

      try {
        apiStats = await ApiSecurityService.getSecurityStats();
      } catch (e) {
        apiStats = {'error': 'فشل في جلب إحصائيات API: $e'};
      }

      try {
        final status = await PermissionsAuditService.checkAppPermissions();
        permissionStatus = status.toJson();
      } catch (e) {
        permissionStatus = {'error': 'فشل في فحص الأذونات: $e'};
      }

      try {
        // في وضع التطوير، استخدم نتائج مبسطة
        if (kDebugMode) {
          firebaseSecurityCheck = {
            'Firestore': {
              'isSecure': true,
              'reason': 'وضع التطوير: تم قبول الاتصال',
              'recommendations': [],
            },
            'Storage': {
              'isSecure': true,
              'reason': 'وضع التطوير: تم قبول الاتصال',
              'recommendations': [],
            },
            'Auth': {
              'isSecure': true,
              'reason': 'وضع التطوير: تم قبول الاتصال',
              'recommendations': [],
            },
            'Functions': {
              'isSecure': true,
              'reason': 'وضع التطوير: تم قبول الاتصال',
              'recommendations': [],
            },
          };
        } else {
          final securityCheck = await SSLPinningService.checkFirebaseSecurity();
          firebaseSecurityCheck = securityCheck.map(
            (key, value) => MapEntry(key, value.toJson()),
          );
        }
      } catch (e) {
        firebaseSecurityCheck = {'error': 'فشل في فحص أمان Firebase: $e'};
      }

      setState(() {
        _securityReport = {
          'authInfo': authInfo,
          'apiStats': apiStats,
          'permissionStatus': permissionStatus,
          'firebaseSecurityCheck': firebaseSecurityCheck,
          'generatedAt': DateTime.now().toIso8601String(),
          'reportVersion': '1.0',
        };
        _isLoading = false;
      });

      if (kDebugMode) {
        print('✅ تم توليد تقرير الأمان بنجاح');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في توليد تقرير الأمان: $e');
      }
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.scaffoldBackground,
      appBar: AppBar(
        title: const Text('تقرير الأمان الشامل'),
        backgroundColor: AppColors.primaryColor,
        foregroundColor: AppColors.white,
        elevation: 0,
        centerTitle: true,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _generateSecurityReport,
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : RefreshIndicator(
              onRefresh: _generateSecurityReport,
              child: SingleChildScrollView(
                physics: const AlwaysScrollableScrollPhysics(),
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // ملخص الأمان العام
                    _buildSecurityOverview(),
                    
                    const SizedBox(height: 24),
                    
                    // تقرير المصادقة
                    _buildAuthenticationReport(),
                    
                    const SizedBox(height: 24),
                    
                    // تقرير API
                    _buildApiSecurityReport(),
                    
                    const SizedBox(height: 24),
                    
                    // تقرير الأذونات
                    _buildPermissionsReport(),
                    
                    const SizedBox(height: 24),
                    
                    // تقرير Firebase
                    _buildFirebaseSecurityReport(),
                    
                    const SizedBox(height: 24),
                    
                    // التوصيات
                    _buildRecommendations(),
                  ],
                ),
              ),
            ),
    );
  }

  Widget _buildSecurityOverview() {
    final authInfo = _securityReport['authInfo'] as Map<String, dynamic>? ?? {};
    final isAuthenticated = authInfo['isAuthenticated'] ?? false;
    final isAdmin = authInfo['isAdmin'] ?? false;

    // حساب نقاط الأمان الإجمالية
    double securityScore = 0.0;
    
    if (isAuthenticated) securityScore += 25.0;
    if (authInfo['securityStats']?['isFullySecure'] == true) securityScore += 25.0;
    if (_securityReport['apiStats']?['successRate'] != null) {
      final successRate = double.tryParse(_securityReport['apiStats']['successRate'].toString()) ?? 0.0;
      securityScore += (successRate / 100) * 25.0;
    }
    if (_securityReport['permissionStatus']?['systemPermissionAudit']?['complianceScore'] != null) {
      final complianceScore = _securityReport['permissionStatus']['systemPermissionAudit']['complianceScore'];
      securityScore += (complianceScore / 100) * 25.0;
    }

    Color scoreColor;
    String scoreText;
    
    if (securityScore >= 80) {
      scoreColor = AppColors.accentColor;
      scoreText = 'ممتاز';
    } else if (securityScore >= 60) {
      scoreColor = AppColors.secondaryColor;
      scoreText = 'جيد';
    } else if (securityScore >= 40) {
      scoreColor = AppColors.frameGold;
      scoreText = 'متوسط';
    } else {
      scoreColor = AppColors.errorColor;
      scoreText = 'ضعيف';
    }

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.security,
                  color: scoreColor,
                  size: 32,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'نقاط الأمان الإجمالية',
                        style: Theme.of(context).textTheme.titleLarge?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Text(
                        '${securityScore.toStringAsFixed(1)}/100 - $scoreText',
                        style: TextStyle(
                          color: scoreColor,
                          fontWeight: FontWeight.bold,
                          fontSize: 18,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 16),
            
            // مؤشر النقاط
            LinearProgressIndicator(
              value: securityScore / 100,
              backgroundColor: Colors.grey[300],
              valueColor: AlwaysStoppedAnimation<Color>(scoreColor),
              minHeight: 8,
            ),
            
            const SizedBox(height: 16),
            
            // معلومات سريعة
            Row(
              children: [
                Expanded(
                  child: _buildQuickStat(
                    'المصادقة',
                    isAuthenticated ? 'مفعلة' : 'غير مفعلة',
                    isAuthenticated ? AppColors.accentColor : AppColors.errorColor,
                  ),
                ),
                Expanded(
                  child: _buildQuickStat(
                    'التشفير',
                    'مفعل',
                    AppColors.accentColor,
                  ),
                ),
                Expanded(
                  child: _buildQuickStat(
                    'الصلاحية',
                    isAdmin ? 'إداري' : 'مستخدم',
                    isAdmin ? AppColors.frameGold : AppColors.primaryColor,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildQuickStat(String label, String value, Color color) {
    return Column(
      children: [
        Text(
          label,
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
            color: AppColors.secondaryText,
          ),
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: 4),
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
          decoration: BoxDecoration(
            color: color.withOpacity(0.1),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: color.withOpacity(0.3)),
          ),
          child: Text(
            value,
            style: TextStyle(
              color: color,
              fontWeight: FontWeight.bold,
              fontSize: 12,
            ),
            textAlign: TextAlign.center,
          ),
        ),
      ],
    );
  }

  Widget _buildAuthenticationReport() {
    final authInfo = _securityReport['authInfo'] as Map<String, dynamic>? ?? {};
    final tokenInfo = authInfo['tokenInfo'] as Map<String, dynamic>?;
    
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.verified_user, color: AppColors.primaryColor),
                const SizedBox(width: 8),
                Text(
                  'تقرير المصادقة والتوكن',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 16),
            
            if (tokenInfo != null) ...[
              _buildInfoRow('نوع التوكن', tokenInfo['tokenType'] ?? 'غير محدد'),
              _buildInfoRow('حالة التوكن', tokenInfo['isExpired'] == true ? 'منتهي الصلاحية' : 'صالح'),
              _buildInfoRow('المُصدر', tokenInfo['issuer'] ?? 'غير محدد'),
              if (tokenInfo['expiresAt'] != null)
                _buildInfoRow('انتهاء الصلاحية', _formatDateTime(
                  _parseDateTime(tokenInfo['expiresAt'])
                )),
            ] else ...[
              const Text('لا توجد معلومات توكن متاحة'),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildApiSecurityReport() {
    final apiStats = _securityReport['apiStats'] as Map<String, dynamic>? ?? {};
    
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.api, color: AppColors.primaryColor),
                const SizedBox(width: 8),
                Text(
                  'تقرير أمان API',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 16),
            
            _buildInfoRow('إجمالي الطلبات', '${apiStats['totalRequests'] ?? 0}'),
            _buildInfoRow('الطلبات الناجحة', '${apiStats['successfulRequests'] ?? 0}'),
            _buildInfoRow('الطلبات الفاشلة', '${apiStats['failedRequests'] ?? 0}'),
            _buildInfoRow('معدل النجاح', '${apiStats['successRate'] ?? '0.0'}%'),
            _buildInfoRow('طلبات آخر 24 ساعة', '${apiStats['requestsLast24h'] ?? 0}'),
            _buildInfoRow('المستخدمين المحظورين', '${apiStats['lockedUsers'] ?? 0}'),
          ],
        ),
      ),
    );
  }

  Widget _buildPermissionsReport() {
    final permissionStatus = _securityReport['permissionStatus'] as Map<String, dynamic>? ?? {};
    final systemAudit = permissionStatus['systemPermissionAudit'] as Map<String, dynamic>? ?? {};
    
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.admin_panel_settings, color: AppColors.primaryColor),
                const SizedBox(width: 8),
                Text(
                  'تقرير الأذونات',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 16),
            
            _buildInfoRow('نوع المستخدم', permissionStatus['isAdmin'] == true ? 'إداري' : 'مستخدم عادي'),
            _buildInfoRow('عدد الأذونات', '${(permissionStatus['userPermissions'] as List?)?.length ?? 0}'),
            _buildInfoRow('نقاط الامتثال', '${systemAudit['complianceScore'] ?? 0.0}%'),
            _buildInfoRow('المشاكل المكتشفة', '${(systemAudit['issues'] as List?)?.length ?? 0}'),
            _buildInfoRow('التوصيات', '${(systemAudit['recommendations'] as List?)?.length ?? 0}'),
          ],
        ),
      ),
    );
  }

  Widget _buildFirebaseSecurityReport() {
    final firebaseCheck = _securityReport['firebaseSecurityCheck'] as Map<String, dynamic>? ?? {};
    
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.cloud_done, color: AppColors.primaryColor),
                const SizedBox(width: 8),
                Text(
                  'تقرير أمان Firebase',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 16),
            
            if (firebaseCheck.isNotEmpty) ...[
              for (final entry in firebaseCheck.entries) ...[
                _buildFirebaseServiceStatus(entry.key, entry.value),
                if (entry.key != firebaseCheck.keys.last) const Divider(),
              ],
            ] else ...[
              const Text('لا توجد معلومات أمان Firebase متاحة'),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildFirebaseServiceStatus(String serviceName, Map<String, dynamic> serviceData) {
    final isSecure = serviceData['isSecure'] ?? false;
    final reason = serviceData['reason'] ?? 'غير محدد';
    
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        children: [
          Icon(
            isSecure ? Icons.check_circle : Icons.error,
            color: isSecure ? AppColors.accentColor : AppColors.errorColor,
            size: 20,
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  serviceName,
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                Text(
                  reason,
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: AppColors.secondaryText,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildRecommendations() {
    final recommendations = <String>[];
    
    // جمع التوصيات من جميع التقارير
    final permissionStatus = _securityReport['permissionStatus'] as Map<String, dynamic>? ?? {};
    final systemAudit = permissionStatus['systemPermissionAudit'] as Map<String, dynamic>? ?? {};
    final systemRecommendations = systemAudit['recommendations'] as List? ?? [];
    
    recommendations.addAll(systemRecommendations.cast<String>());
    
    // إضافة توصيات عامة
    recommendations.addAll([
      'قم بتحديث كلمة المرور بانتظام',
      'فعّل المصادقة الثنائية عند توفرها',
      'راجع الأذونات الممنوحة للتطبيق',
      'تأكد من تحديث التطبيق للإصدار الأحدث',
      'لا تشارك معلومات تسجيل الدخول مع الآخرين',
    ]);
    
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.lightbulb, color: AppColors.frameGold),
                const SizedBox(width: 8),
                Text(
                  'التوصيات الأمنية',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 16),
            
            if (recommendations.isNotEmpty) ...[
              for (int i = 0; i < recommendations.length && i < 10; i++) ...[
                Padding(
                  padding: const EdgeInsets.symmetric(vertical: 4),
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Container(
                        width: 6,
                        height: 6,
                        margin: const EdgeInsets.only(top: 8, left: 8),
                        decoration: const BoxDecoration(
                          color: AppColors.frameGold,
                          shape: BoxShape.circle,
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Text(
                          recommendations[i],
                          style: Theme.of(context).textTheme.bodyMedium,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ] else ...[
              const Text('لا توجد توصيات إضافية'),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Expanded(
            flex: 2,
            child: Text(
              label,
              style: Theme.of(context).textTheme.bodyMedium,
            ),
          ),
          Expanded(
            flex: 3,
            child: Text(
              value,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                fontWeight: FontWeight.w500,
              ),
              textAlign: TextAlign.end,
            ),
          ),
        ],
      ),
    );
  }

  DateTime? _parseDateTime(dynamic value) {
    if (value == null) return null;

    try {
      if (value is String) {
        return DateTime.parse(value);
      } else if (value is DateTime) {
        return value;
      } else if (value is int) {
        return DateTime.fromMillisecondsSinceEpoch(value);
      }
    } catch (e) {
      // تجاهل الأخطاء وإرجاع null
    }

    return null;
  }

  String _formatDateTime(DateTime? dateTime) {
    if (dateTime == null) return 'غير محدد';

    try {
      return '${dateTime.day.toString().padLeft(2, '0')}/${dateTime.month.toString().padLeft(2, '0')}/${dateTime.year} ${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
    } catch (e) {
      return 'تاريخ غير صالح';
    }
  }
}
