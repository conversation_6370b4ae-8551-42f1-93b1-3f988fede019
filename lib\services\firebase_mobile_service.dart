import 'package:flutter/foundation.dart';
import 'dart:async';
import 'package:firebase_core/firebase_core.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import '../models/product.dart';
import '../models/review.dart';

/// خدمة Firebase للموبايل (Android/iOS) بدون استخدام dart:js
class FirebaseMobileService {
  static bool _isInitialized = false;

  /// طباعة رسالة في Console
  static void _logMessage(String message) {
    if (kDebugMode) {
      debugPrint('📱 Firebase Mobile: $message');
    }
  }

  /// تهيئة الخدمة
  static Future<bool> initialize() async {
    try {
      if (_isInitialized) {
        _logMessage('Firebase Mobile مُهيأ مسبقاً');
        return true;
      }

      _logMessage('🔥 بدء تهيئة Firebase Mobile Service...');

      // تهيئة Firebase إذا لم يكن مُهيأ
      if (Firebase.apps.isEmpty) {
        await Firebase.initializeApp();
        _logMessage('🔥 تم تهيئة Firebase Core');
      } else {
        _logMessage('🔥 Firebase Core مُهيأ مسبقاً');
      }

      // تهيئة Firestore
      FirebaseFirestore.instance.settings = const Settings(
        persistenceEnabled: true,
        cacheSizeBytes: Settings.CACHE_SIZE_UNLIMITED,
      );

      _isInitialized = true;
      _logMessage('✅ تم تهيئة Firebase Mobile بنجاح');
      return true;
    } catch (e) {
      _logMessage('❌ خطأ في تهيئة Firebase Mobile Service: $e');
      return false;
    }
  }

  /// جلب المنتجات من Firestore (نسخة للموبايل)
  static Future<List<Product>> getProducts() async {
    try {
      _logMessage('🔄 جلب المنتجات من Firestore (Mobile)...');

      if (!_isInitialized) {
        _logMessage('⚠️ Firebase Mobile Service غير مُهيأ، جاري التهيئة...');
        await initialize();
      }

      _logMessage('🔗 محاولة الاتصال بـ Firestore collection: products');

      // جلب المنتجات من Firestore
      final QuerySnapshot snapshot = await FirebaseFirestore.instance
          .collection('products')
          .get();

      _logMessage('📊 تم استلام ${snapshot.docs.length} وثيقة من Firestore');

      List<Product> products = [];
      for (var doc in snapshot.docs) {
        try {
          final data = doc.data() as Map<String, dynamic>;
          data['id'] = doc.id; // إضافة ID الوثيقة

          _logMessage(
            '📦 معالجة منتج: ${data['name'] ?? 'بدون اسم'} (${doc.id})',
          );

          final product = Product.fromJson(data);
          products.add(product);
        } catch (e) {
          _logMessage('⚠️ خطأ في تحويل منتج ${doc.id}: $e');
        }
      }

      _logMessage('✅ تم جلب ${products.length} منتج بنجاح من Firestore');

      // طباعة أسماء أول 3 منتجات للتأكد
      for (int i = 0; i < products.length && i < 3; i++) {
        _logMessage('📦 منتج ${i + 1}: ${products[i].name}');
      }

      return products;
    } catch (e) {
      _logMessage('❌ خطأ في جلب المنتجات من Firestore: $e');
      _logMessage('❌ نوع الخطأ: ${e.runtimeType}');
      return [];
    }
  }

  /// إضافة منتج إلى Firestore
  static Future<bool> addProduct(Product product) async {
    try {
      _logMessage('🔄 إضافة منتج: ${product.name}');

      if (!_isInitialized) {
        await initialize();
      }

      // تحويل المنتج إلى JSON
      final productData = product.toJson();

      // استخدام المعرف المخصص بدلاً من التلقائي
      final productId = product.id.isNotEmpty ? product.id : DateTime.now().millisecondsSinceEpoch.toString();
      productData['id'] = productId;

      // إضافة المنتج إلى Firestore باستخدام المعرف المخصص
      await FirebaseFirestore.instance
          .collection('products')
          .doc(productId)
          .set(productData);

      _logMessage('✅ تم إضافة المنتج بنجاح بالمعرف: $productId');
      return true;
    } catch (e) {
      _logMessage('❌ خطأ في إضافة المنتج: $e');
      return false;
    }
  }

  /// تعديل منتج في Firestore
  static Future<bool> updateProduct(Product product) async {
    try {
      _logMessage('🔄 تعديل منتج: ${product.name}');

      if (!_isInitialized) {
        await initialize();
      }

      if (product.id.isEmpty) {
        _logMessage('❌ ID المنتج مطلوب للتعديل');
        return false;
      }

      // تحويل المنتج إلى JSON وتحديثه في Firestore
      final productData = product.toJson();
      productData.remove('id'); // إزالة ID من البيانات

      await FirebaseFirestore.instance
          .collection('products')
          .doc(product.id)
          .update(productData);

      _logMessage('✅ تم تعديل المنتج بنجاح');
      return true;
    } catch (e) {
      _logMessage('❌ خطأ في تعديل المنتج: $e');
      return false;
    }
  }

  /// حذف منتج من Firestore
  static Future<bool> deleteProduct(String productId) async {
    try {
      _logMessage('🔄 حذف منتج: $productId');

      if (!_isInitialized) {
        await initialize();
      }

      if (productId.isEmpty) {
        _logMessage('❌ ID المنتج مطلوب للحذف');
        return false;
      }

      // حذف المنتج من Firestore
      await FirebaseFirestore.instance
          .collection('products')
          .doc(productId)
          .delete();

      _logMessage('✅ تم حذف المنتج بنجاح');
      return true;
    } catch (e) {
      _logMessage('❌ خطأ في حذف المنتج: $e');
      return false;
    }
  }

  /// جلب الفئات من Firestore
  static Future<List<Map<String, dynamic>>> getCategories() async {
    try {
      _logMessage('🔄 جلب الفئات من Firestore (Mobile)...');

      if (!_isInitialized) {
        _logMessage('⚠️ Firebase Mobile Service غير مُهيأ، جاري التهيئة...');
        await initialize();
      }

      _logMessage('🔗 محاولة الاتصال بـ Firestore collection: categories');

      // جلب الفئات من Firestore
      final QuerySnapshot snapshot = await FirebaseFirestore.instance
          .collection('categories')
          .get();

      _logMessage('📊 تم استلام ${snapshot.docs.length} فئة من Firestore');

      List<Map<String, dynamic>> categories = [];
      for (var doc in snapshot.docs) {
        final data = doc.data() as Map<String, dynamic>;
        data['id'] = doc.id; // إضافة ID الوثيقة

        _logMessage('📂 معالجة فئة: ${data['name'] ?? 'بدون اسم'} (${doc.id})');
        categories.add(data);
      }

      _logMessage('✅ تم جلب ${categories.length} فئة بنجاح من Firestore');

      // طباعة أسماء أول 3 فئات للتأكد
      for (int i = 0; i < categories.length && i < 3; i++) {
        _logMessage('📂 فئة ${i + 1}: ${categories[i]['name']}');
      }

      return categories;
    } catch (e) {
      _logMessage('❌ خطأ في جلب الفئات من Firestore: $e');
      _logMessage('❌ نوع الخطأ: ${e.runtimeType}');
      return [];
    }
  }

  /// إضافة فئة جديدة
  static Future<bool> addCategory(Map<String, dynamic> categoryData) async {
    try {
      _logMessage('🔄 إضافة فئة: ${categoryData['name']}');

      if (!_isInitialized) {
        await initialize();
      }

      // استخدام المعرف المخصص بدلاً من التلقائي
      final categoryId = categoryData['id'] ?? DateTime.now().millisecondsSinceEpoch.toString();
      categoryData['id'] = categoryId;

      // إضافة الفئة إلى Firestore باستخدام المعرف المخصص
      await FirebaseFirestore.instance
          .collection('categories')
          .doc(categoryId)
          .set(categoryData);

      _logMessage('✅ تم إضافة الفئة بنجاح بالمعرف: $categoryId');
      return true;
    } catch (e) {
      _logMessage('❌ خطأ في إضافة الفئة: $e');
      return false;
    }
  }

  /// تعديل فئة
  static Future<bool> updateCategory(Map<String, dynamic> categoryData) async {
    try {
      _logMessage('🔄 تعديل فئة: ${categoryData['name']}');

      if (!_isInitialized) {
        await initialize();
      }

      if (categoryData['id'] == null || categoryData['id'].isEmpty) {
        _logMessage('❌ ID الفئة مطلوب للتعديل');
        return false;
      }

      final categoryId = categoryData['id'];
      final updateData = Map<String, dynamic>.from(categoryData);
      updateData.remove('id'); // إزالة ID من البيانات

      // تحديث الفئة في Firestore
      await FirebaseFirestore.instance
          .collection('categories')
          .doc(categoryId)
          .update(updateData);

      _logMessage('✅ تم تعديل الفئة بنجاح');
      return true;
    } catch (e) {
      _logMessage('❌ خطأ في تعديل الفئة: $e');
      return false;
    }
  }

  /// حذف فئة
  static Future<bool> deleteCategory(String categoryId) async {
    try {
      _logMessage('🔄 حذف فئة: $categoryId');

      if (!_isInitialized) {
        await initialize();
      }

      if (categoryId.isEmpty) {
        _logMessage('❌ ID الفئة مطلوب للحذف');
        return false;
      }

      // حذف الفئة من Firestore
      await FirebaseFirestore.instance
          .collection('categories')
          .doc(categoryId)
          .delete();

      _logMessage('✅ تم حذف الفئة بنجاح');
      return true;
    } catch (e) {
      _logMessage('❌ خطأ في حذف الفئة: $e');
      return false;
    }
  }

  /// جلب المراجعات
  static Future<List<Review>> getReviews(String productId) async {
    try {
      _logMessage('🔄 جلب المراجعات للمنتج: $productId');

      if (!_isInitialized) {
        await initialize();
      }

      _logMessage('⚠️ خدمة جلب المراجعات قيد التطوير');
      return [];
    } catch (e) {
      _logMessage('❌ خطأ في جلب المراجعات: $e');
      return [];
    }
  }

  /// إضافة مراجعة
  static Future<bool> addReview(Review review) async {
    try {
      _logMessage('🔄 إضافة مراجعة للمنتج: ${review.productId}');

      if (!_isInitialized) {
        await initialize();
      }

      // تحويل المراجعة إلى JSON وإضافتها لـ Firestore
      final reviewData = review.toJson();
      reviewData.remove('id'); // إزالة ID لأن Firestore سيولده

      await FirebaseFirestore.instance.collection('reviews').add(reviewData);

      _logMessage('✅ تم إضافة المراجعة بنجاح');
      return true;
    } catch (e) {
      _logMessage('❌ خطأ في إضافة المراجعة: $e');
      return false;
    }
  }

  /// جلب الطلبات
  static Future<List<Map<String, dynamic>>> getOrders() async {
    try {
      _logMessage('🔄 جلب الطلبات (Mobile)...');

      if (!_isInitialized) {
        _logMessage('⚠️ Firebase Mobile Service غير مُهيأ، جاري التهيئة...');
        await initialize();
      }

      _logMessage('🔗 محاولة الاتصال بـ Firestore collection: orders');
      _logMessage('🔥 Firebase App: ${Firebase.app().name}');
      _logMessage('🔥 Project ID: ${Firebase.app().options.projectId}');

      // جلب الطلبات من Firestore (بدون ترتيب لتجنب مشاكل الحقول المفقودة)
      final QuerySnapshot snapshot = await FirebaseFirestore.instance
          .collection('orders')
          .get();

      _logMessage('📊 تم استلام ${snapshot.docs.length} طلب من Firestore');

      if (snapshot.docs.isEmpty) {
        _logMessage('⚠️ لا توجد طلبات في collection orders');
        _logMessage('🔍 تحقق من أن البيانات موجودة في Firestore Console');
        return [];
      }

      List<Map<String, dynamic>> orders = [];
      for (var doc in snapshot.docs) {
        final data = doc.data() as Map<String, dynamic>;

        // تحويل Timestamp إلى String إذا لزم الأمر
        final processedData = <String, dynamic>{};
        for (final entry in data.entries) {
          final key = entry.key;
          final value = entry.value;

          if (value is Timestamp) {
            // تحويل Timestamp إلى ISO string
            processedData[key] = value.toDate().toIso8601String();
          } else {
            processedData[key] = value;
          }
        }

        processedData['id'] = doc.id; // إضافة ID الوثيقة

        _logMessage(
          '📋 معالجة طلب: ${processedData['orderNumber'] ?? processedData['id']} (${doc.id})',
        );
        orders.add(processedData);
      }

      _logMessage('✅ تم جلب ${orders.length} طلب بنجاح من Firestore');

      // ترتيب الطلبات حسب التاريخ (الأحدث أولاً)
      orders.sort((a, b) {
        try {
          final aTime = a['createdAt'];
          final bTime = b['createdAt'];

          if (aTime == null && bTime == null) return 0;
          if (aTime == null) return 1;
          if (bTime == null) return -1;

          // إذا كان التاريخ رقم (timestamp)
          if (aTime is num && bTime is num) {
            return bTime.compareTo(aTime);
          }

          // إذا كان التاريخ نص
          if (aTime is String && bTime is String) {
            return bTime.compareTo(aTime);
          }

          return 0;
        } catch (e) {
          _logMessage('⚠️ خطأ في ترتيب الطلبات: $e');
          return 0;
        }
      });

      // طباعة أول 5 طلبات للتأكد
      for (int i = 0; i < orders.length && i < 5; i++) {
        final order = orders[i];
        _logMessage(
          '📋 طلب ${i + 1}: ${order['orderNumber'] ?? order['id']} - الحالة: ${order['status'] ?? 'غير محدد'} - التاريخ: ${order['createdAt']}',
        );
      }

      return orders;
    } catch (e) {
      _logMessage('❌ خطأ في جلب الطلبات من Firestore: $e');
      _logMessage('❌ نوع الخطأ: ${e.runtimeType}');
      return [];
    }
  }

  /// إضافة طلب جديد
  static Future<bool> addOrder(Map<String, dynamic> orderData) async {
    try {
      _logMessage('🔄 إضافة طلب جديد');

      if (!_isInitialized) {
        await initialize();
      }

      // استخدام المعرف المخصص بدلاً من التلقائي
      final orderId = orderData['id'] ?? DateTime.now().millisecondsSinceEpoch.toString();

      // إنشاء نسخة من البيانات لتجنب تعديل الأصل
      final orderDataCopy = Map<String, dynamic>.from(orderData);
      orderDataCopy['id'] = orderId;

      // إضافة timestamp كـ ISO string بدلاً من FieldValue
      final now = DateTime.now().toIso8601String();
      if (!orderDataCopy.containsKey('createdAt')) {
        orderDataCopy['createdAt'] = now;
      }
      if (!orderDataCopy.containsKey('updatedAt')) {
        orderDataCopy['updatedAt'] = now;
      }

      // إضافة الطلب إلى Firestore باستخدام المعرف المخصص
      await FirebaseFirestore.instance
          .collection('orders')
          .doc(orderId)
          .set(orderDataCopy);

      _logMessage('✅ تم إضافة الطلب بنجاح بالمعرف: $orderId');
      return true;
    } catch (e) {
      _logMessage('❌ خطأ في إضافة الطلب: $e');
      return false;
    }
  }

  /// تحديث طلب
  static Future<bool> updateOrder(
    String orderId,
    Map<String, dynamic> orderData,
  ) async {
    try {
      _logMessage('🔄 تحديث طلب: $orderId');
      _logMessage('📝 البيانات المرسلة: $orderData');

      if (!_isInitialized) {
        await initialize();
      }

      if (orderId.isEmpty) {
        _logMessage('❌ ID الطلب مطلوب للتحديث');
        return false;
      }

      // إنشاء نسخة من البيانات لتجنب تعديل الأصل
      final orderDataCopy = Map<String, dynamic>.from(orderData);

      // إضافة timestamp كـ ISO string بدلاً من FieldValue
      orderDataCopy['updatedAt'] = DateTime.now().toIso8601String();

      _logMessage('📝 البيانات النهائية للتحديث: $orderDataCopy');

      // تحديث الطلب في Firestore
      await FirebaseFirestore.instance
          .collection('orders')
          .doc(orderId)
          .update(orderDataCopy);

      _logMessage('✅ تم تحديث الطلب بنجاح في Firestore');
      return true;
    } catch (e) {
      _logMessage('❌ خطأ في تحديث الطلب: $e');
      return false;
    }
  }

  /// جلب المستخدمين
  static Future<List<Map<String, dynamic>>> getUsers() async {
    try {
      _logMessage('🔄 جلب المستخدمين (Mobile)...');

      if (!_isInitialized) {
        _logMessage('⚠️ Firebase Mobile Service غير مُهيأ، جاري التهيئة...');
        await initialize();
      }

      _logMessage('🔗 محاولة الاتصال بـ Firestore collection: users');

      // جلب المستخدمين من Firestore
      final QuerySnapshot snapshot = await FirebaseFirestore.instance
          .collection('users')
          .get();

      _logMessage('📊 تم استلام ${snapshot.docs.length} مستخدم من Firestore');

      List<Map<String, dynamic>> users = [];
      for (var doc in snapshot.docs) {
        final data = doc.data() as Map<String, dynamic>;
        data['id'] = doc.id; // إضافة ID الوثيقة

        _logMessage(
          '👤 معالجة مستخدم: ${data['email'] ?? 'بدون إيميل'} (${doc.id})',
        );
        users.add(data);
      }

      _logMessage('✅ تم جلب ${users.length} مستخدم بنجاح من Firestore');

      return users;
    } catch (e) {
      _logMessage('❌ خطأ في جلب المستخدمين من Firestore: $e');
      _logMessage('❌ نوع الخطأ: ${e.runtimeType}');
      return [];
    }
  }

  /// جلب العلامات التجارية
  static Future<List<Map<String, dynamic>>> getBrands() async {
    try {
      _logMessage('🔄 جلب العلامات التجارية (Mobile)...');

      if (!_isInitialized) {
        _logMessage('⚠️ Firebase Mobile Service غير مُهيأ، جاري التهيئة...');
        await initialize();
      }

      _logMessage('🔗 محاولة الاتصال بـ Firestore collection: brands');

      // جلب العلامات التجارية من Firestore
      final QuerySnapshot snapshot = await FirebaseFirestore.instance
          .collection('brands')
          .get();

      _logMessage(
        '📊 تم استلام ${snapshot.docs.length} علامة تجارية من Firestore',
      );

      List<Map<String, dynamic>> brands = [];
      for (var doc in snapshot.docs) {
        final data = doc.data() as Map<String, dynamic>;
        data['id'] = doc.id; // إضافة ID الوثيقة

        _logMessage(
          '🏷️ معالجة علامة تجارية: ${data['name'] ?? 'بدون اسم'} (${doc.id})',
        );
        brands.add(data);
      }

      _logMessage('✅ تم جلب ${brands.length} علامة تجارية بنجاح من Firestore');

      // طباعة أسماء أول 3 علامات تجارية للتأكد
      for (int i = 0; i < brands.length && i < 3; i++) {
        _logMessage('🏷️ علامة تجارية ${i + 1}: ${brands[i]['name']}');
      }

      return brands;
    } catch (e) {
      _logMessage('❌ خطأ في جلب العلامات التجارية من Firestore: $e');
      _logMessage('❌ نوع الخطأ: ${e.runtimeType}');
      return [];
    }
  }

  /// إضافة علامة تجارية
  static Future<bool> addBrand(Map<String, dynamic> brandData) async {
    try {
      _logMessage('🔄 إضافة علامة تجارية: ${brandData['name']}');

      if (!_isInitialized) {
        await initialize();
      }

      // استخدام المعرف المخصص بدلاً من التلقائي
      final brandId = brandData['id'] ?? DateTime.now().millisecondsSinceEpoch.toString();
      brandData['id'] = brandId;

      // إضافة العلامة التجارية إلى Firestore باستخدام المعرف المخصص
      await FirebaseFirestore.instance
          .collection('brands')
          .doc(brandId)
          .set(brandData);

      _logMessage('✅ تم إضافة العلامة التجارية بنجاح بالمعرف: $brandId');
      return true;
    } catch (e) {
      _logMessage('❌ خطأ في إضافة العلامة التجارية: $e');
      return false;
    }
  }

  /// تعديل علامة تجارية
  static Future<bool> updateBrand(Map<String, dynamic> brandData) async {
    try {
      _logMessage('🔄 تعديل علامة تجارية: ${brandData['name']}');

      if (!_isInitialized) {
        await initialize();
      }

      if (brandData['id'] == null || brandData['id'].isEmpty) {
        _logMessage('❌ ID العلامة التجارية مطلوب للتعديل');
        return false;
      }

      final brandId = brandData['id'];
      final updateData = Map<String, dynamic>.from(brandData);
      updateData.remove('id'); // إزالة ID من البيانات

      // تحديث العلامة التجارية في Firestore
      await FirebaseFirestore.instance
          .collection('brands')
          .doc(brandId)
          .update(updateData);

      _logMessage('✅ تم تعديل العلامة التجارية بنجاح');
      return true;
    } catch (e) {
      _logMessage('❌ خطأ في تعديل العلامة التجارية: $e');
      return false;
    }
  }

  /// حذف علامة تجارية
  static Future<bool> deleteBrand(String brandId) async {
    try {
      _logMessage('🔄 حذف علامة تجارية: $brandId');

      if (!_isInitialized) {
        await initialize();
      }

      if (brandId.isEmpty) {
        _logMessage('❌ ID العلامة التجارية مطلوب للحذف');
        return false;
      }

      // حذف العلامة التجارية من Firestore
      await FirebaseFirestore.instance
          .collection('brands')
          .doc(brandId)
          .delete();

      _logMessage('✅ تم حذف العلامة التجارية بنجاح');
      return true;
    } catch (e) {
      _logMessage('❌ خطأ في حذف العلامة التجارية: $e');
      return false;
    }
  }

  /// جلب مراجعات المنتج
  static Future<List<Review>> getProductReviews(String productId) async {
    try {
      _logMessage('🔄 جلب مراجعات المنتج: $productId');

      if (!_isInitialized) {
        await initialize();
      }

      // جلب المراجعات من Firestore
      final QuerySnapshot snapshot = await FirebaseFirestore.instance
          .collection('reviews')
          .where('productId', isEqualTo: productId)
          .orderBy('createdAt', descending: true)
          .get();

      List<Review> reviews = [];
      for (var doc in snapshot.docs) {
        try {
          final data = doc.data() as Map<String, dynamic>;
          data['id'] = doc.id; // إضافة ID الوثيقة

          final review = Review.fromJson(data);
          reviews.add(review);
        } catch (e) {
          _logMessage('⚠️ خطأ في تحويل مراجعة: $e');
        }
      }

      _logMessage('✅ تم جلب ${reviews.length} مراجعة');
      return reviews;
    } catch (e) {
      _logMessage('❌ خطأ في جلب مراجعات المنتج: $e');
      return [];
    }
  }
}

/// Alias للاستيراد المشروط - يشير إلى نفس الكلاس
class FirebaseWebService extends FirebaseMobileService {}
