import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../app_properties.dart';

class ContactUsPage extends StatefulWidget {
  const ContactUsPage({super.key});

  @override
  State<ContactUsPage> createState() => _ContactUsPageState();
}

class _ContactUsPageState extends State<ContactUsPage> {
  final List<ContactMethod> _contactMethods = [
    ContactMethod(
      title: 'الهاتف',
      subtitle: '+966 11 234 5678',
      icon: Icons.phone,
      color: AppColors.success,
      action: 'اتصال',
    ),
    ContactMethod(
      title: 'واتساب',
      subtitle: '+966 50 123 4567',
      icon: Icons.chat,
      color: Colors.green,
      action: 'محادثة',
    ),
    ContactMethod(
      title: 'البريد الإلكتروني',
      subtitle: '<EMAIL>',
      icon: Icons.email,
      color: AppColors.primaryColor,
      action: 'إرسال',
    ),
    ContactMethod(
      title: 'تويتر',
      subtitle: '@VisionLensApp',
      icon: Icons.alternate_email,
      color: Colors.blue,
      action: 'متابعة',
    ),
  ];

  final List<StoreLocation> _storeLocations = [
    StoreLocation(
      name: 'فرع الرياض الرئيسي',
      address: 'شارع الملك فهد، حي العليا، الرياض',
      phone: '+966 11 234 5678',
      hours: 'السبت - الخميس: 9:00 ص - 10:00 م\nالجمعة: 2:00 م - 10:00 م',
      coordinates: '24.7136° N, 46.6753° E',
    ),
    StoreLocation(
      name: 'فرع جدة',
      address: 'طريق الملك عبدالعزيز، حي الروضة، جدة',
      phone: '+966 12 345 6789',
      hours: 'السبت - الخميس: 9:00 ص - 10:00 م\nالجمعة: 2:00 م - 10:00 م',
      coordinates: '21.3891° N, 39.8579° E',
    ),
    StoreLocation(
      name: 'فرع الدمام',
      address: 'شارع الأمير محمد بن فهد، حي الفيصلية، الدمام',
      phone: '+966 13 456 7890',
      hours: 'السبت - الخميس: 9:00 ص - 10:00 م\nالجمعة: 2:00 م - 10:00 م',
      coordinates: '26.4207° N, 50.0888° E',
    ),
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.backgroundColor,
      appBar: AppBar(
        backgroundColor: AppColors.primaryColor,
        foregroundColor: AppColors.white,
        title: const Row(
          children: [
            Icon(Icons.contact_support, size: 24),
            SizedBox(width: 8),
            Text('تواصل معنا'),
          ],
        ),
        elevation: 0,
      ),
      body: SingleChildScrollView(
        child: Column(
          children: [
            // رسالة ترحيبية
            _buildWelcomeSection(),
            
            // طرق التواصل
            _buildContactMethodsSection(),
            
            // أوقات العمل
            _buildWorkingHoursSection(),
            
            // مواقع الفروع
            _buildStoreLocationsSection(),
            
            const SizedBox(height: 20),
          ],
        ),
      ),
    );
  }

  Widget _buildWelcomeSection() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            AppColors.primaryColor,
            AppColors.primaryColor.withValues(alpha: 0.8),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
      ),
      child: Column(
        children: [
          const Icon(
            Icons.support_agent,
            color: AppColors.white,
            size: 64,
          ),
          const SizedBox(height: 16),
          const Text(
            'نحن هنا لمساعدتك',
            style: TextStyle(
              color: AppColors.white,
              fontSize: 24,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'فريق خدمة العملاء جاهز للإجابة على جميع استفساراتك',
            textAlign: TextAlign.center,
            style: TextStyle(
              color: AppColors.white.withValues(alpha: 0.9),
              fontSize: 16,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildContactMethodsSection() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'طرق التواصل',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          ...(_contactMethods.map((method) => _buildContactMethodCard(method))),
        ],
      ),
    );
  }

  Widget _buildContactMethodCard(ContactMethod method) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      child: InkWell(
        onTap: () => _handleContactMethodTap(method),
        borderRadius: BorderRadius.circular(12),
        child: Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: AppColors.white,
            borderRadius: BorderRadius.circular(12),
            boxShadow: [
              BoxShadow(
                color: AppColors.black.withValues(alpha: 0.05),
                blurRadius: 10,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: method.color.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(10),
                ),
                child: Icon(
                  method.icon,
                  color: method.color,
                  size: 24,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      method.title,
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      method.subtitle,
                      style: TextStyle(
                        fontSize: 14,
                        color: AppColors.textColor.withValues(alpha: 0.7),
                      ),
                    ),
                  ],
                ),
              ),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                decoration: BoxDecoration(
                  color: method.color,
                  borderRadius: BorderRadius.circular(20),
                ),
                child: Text(
                  method.action,
                  style: const TextStyle(
                    color: AppColors.white,
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildWorkingHoursSection() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: AppColors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: AppColors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: AppColors.primaryColor.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Icon(
                  Icons.access_time,
                  color: AppColors.primaryColor,
                  size: 20,
                ),
              ),
              const SizedBox(width: 12),
              const Text(
                'أوقات العمل',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          _buildWorkingHourRow('السبت - الخميس', '9:00 ص - 10:00 م'),
          _buildWorkingHourRow('الجمعة', '2:00 م - 10:00 م'),
          const SizedBox(height: 12),
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: AppColors.success.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Row(
              children: [
                const Icon(
                  Icons.check_circle,
                  color: AppColors.success,
                  size: 16,
                ),
                const SizedBox(width: 8),
                Text(
                  'نحن متاحون الآن للمساعدة',
                  style: const TextStyle(
                    color: AppColors.success,
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildWorkingHourRow(String day, String hours) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            day,
            style: const TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w500,
            ),
          ),
          Text(
            hours,
            style: TextStyle(
              fontSize: 14,
              color: AppColors.textColor.withValues(alpha: 0.7),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStoreLocationsSection() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'مواقع الفروع',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          ...(_storeLocations.map((store) => _buildStoreLocationCard(store))),
        ],
      ),
    );
  }

  Widget _buildStoreLocationCard(StoreLocation store) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: AppColors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: AppColors.primaryColor.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Icon(
                  Icons.store,
                  color: AppColors.primaryColor,
                  size: 20,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Text(
                  store.name,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          _buildStoreInfoRow(Icons.location_on, store.address),
          _buildStoreInfoRow(Icons.phone, store.phone),
          _buildStoreInfoRow(Icons.access_time, store.hours),
          const SizedBox(height: 12),
          Row(
            children: [
              Expanded(
                child: OutlinedButton.icon(
                  onPressed: () => _copyToClipboard(store.coordinates),
                  icon: const Icon(Icons.copy, size: 16),
                  label: const Text('نسخ الإحداثيات'),
                  style: OutlinedButton.styleFrom(
                    foregroundColor: AppColors.primaryColor,
                    side: const BorderSide(color: AppColors.primaryColor),
                  ),
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: ElevatedButton.icon(
                  onPressed: () => _openMaps(store.coordinates),
                  icon: const Icon(Icons.directions, size: 16),
                  label: const Text('الاتجاهات'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.primaryColor,
                    foregroundColor: AppColors.white,
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildStoreInfoRow(IconData icon, String text) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Icon(
            icon,
            size: 16,
            color: AppColors.textColor.withValues(alpha: 0.6),
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              text,
              style: TextStyle(
                fontSize: 14,
                color: AppColors.textColor.withValues(alpha: 0.8),
                height: 1.4,
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _handleContactMethodTap(ContactMethod method) {
    // محاكاة فتح التطبيق المناسب
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('فتح ${method.title}: ${method.subtitle}'),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(10),
        ),
      ),
    );
  }

  void _copyToClipboard(String text) {
    Clipboard.setData(ClipboardData(text: text));
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Row(
          children: [
            Icon(Icons.check, color: AppColors.white),
            SizedBox(width: 8),
            Text('تم نسخ الإحداثيات'),
          ],
        ),
        behavior: SnackBarBehavior.floating,
        backgroundColor: AppColors.success,
      ),
    );
  }

  void _openMaps(String coordinates) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('فتح الخرائط: $coordinates'),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(10),
        ),
      ),
    );
  }
}

class ContactMethod {
  final String title;
  final String subtitle;
  final IconData icon;
  final Color color;
  final String action;

  ContactMethod({
    required this.title,
    required this.subtitle,
    required this.icon,
    required this.color,
    required this.action,
  });
}

class StoreLocation {
  final String name;
  final String address;
  final String phone;
  final String hours;
  final String coordinates;

  StoreLocation({
    required this.name,
    required this.address,
    required this.phone,
    required this.hours,
    required this.coordinates,
  });
}
