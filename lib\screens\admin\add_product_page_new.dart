import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'dart:typed_data';
import 'dart:convert';
import 'package:file_picker/file_picker.dart';
import 'package:image/image.dart' as img;
import '../../app_properties.dart';
import '../../models/product.dart';
import '../../models/category.dart' as category_model;
import '../../api_service_mock.dart';
import '../../services/firestore_data_service.dart';
import '../../services/firebase_storage_service.dart';

class AddProductPage extends StatefulWidget {
  const AddProductPage({super.key});

  @override
  State<AddProductPage> createState() => _AddProductPageState();
}

class _AddProductPageState extends State<AddProductPage> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _priceController = TextEditingController();
  final _originalPriceController = TextEditingController();
  final _stockController = TextEditingController();
  final _brandController = TextEditingController();
  final _materialController = TextEditingController();
  final _colorController = TextEditingController();
  final _sizeController = TextEditingController();

  List<category_model.Category> _categories = [];
  String? _selectedCategoryId;
  ProductType _selectedType = ProductType.eyeglasses;
  bool _isLoading = false;
  bool _isFeatured = false;
  bool _isOnSale = false;
  bool _isNew = true;

  @override
  void initState() {
    super.initState();
    _loadCategories();
  }

  Future<void> _loadCategories() async {
    try {
      final categories = await FirestoreDataService.getCategories();
      setState(() {
        _categories = categories;
        if (_categories.isNotEmpty) {
          _selectedCategoryId = _categories.first.id;
        }
      });
      if (kDebugMode) {
        debugPrint('✅ تم تحميل ${_categories.length} فئة');
        debugPrint('🔍 الفئة المحددة: $_selectedCategoryId');
      }
    } catch (e) {
      if (kDebugMode) debugPrint('❌ خطأ في تحميل الفئات: $e');
    }
  }

  // متغيرات الصورة
  Uint8List? _selectedImageBytes;
  String? _selectedImageName;

  /// اختيار صورة من الجهاز
  Future<void> _pickImage() async {
    try {
      final result = await FilePicker.platform.pickFiles(
        type: FileType.image,
        allowMultiple: false,
      );

      if (result != null && result.files.isNotEmpty) {
        final file = result.files.first;
        if (file.bytes != null) {
          // التحقق من صحة الصورة
          if (FirebaseStorageService.isValidImage(file.bytes!, file.name)) {
            setState(() {
              _selectedImageBytes = file.bytes;
              _selectedImageName = file.name;
            });

            if (kDebugMode) {
              debugPrint('✅ تم اختيار الصورة: ${file.name}');
              debugPrint('📏 حجم الصورة: ${file.bytes!.length} بايت');
            }
          } else {
            if (mounted) {
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('الصورة غير صالحة أو كبيرة جداً'),
                  backgroundColor: AppColors.errorColor,
                ),
              );
            }
          }
        }
      }
    } catch (e) {
      if (kDebugMode) {
        debugPrint('❌ خطأ في اختيار الصورة: $e');
      }
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في اختيار الصورة: $e'),
            backgroundColor: AppColors.errorColor,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.scaffoldBackground,
      appBar: AppBar(
        title: const Text('إضافة منتج جديد'),
        backgroundColor: AppColors.primaryColor,
        foregroundColor: AppColors.white,
        elevation: 0,
        centerTitle: true,
        actions: [
          TextButton(
            onPressed: _isLoading ? null : _saveProduct,
            child: const Text(
              'حفظ',
              style: TextStyle(
                color: AppColors.white,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      ),
      body: Form(
        key: _formKey,
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            children: [
              // معلومات أساسية
              _buildBasicInfoCard(),
              const SizedBox(height: 16),

              // صورة المنتج
              _buildImageCard(),
              const SizedBox(height: 16),

              // معلومات السعر والمخزون
              _buildPriceStockCard(),
              const SizedBox(height: 16),

              // تفاصيل المنتج
              _buildProductDetailsCard(),
              const SizedBox(height: 16),

              // إعدادات المنتج
              _buildProductSettingsCard(),
              const SizedBox(height: 24),

              // زر الحفظ
              _buildSaveButton(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildBasicInfoCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'المعلومات الأساسية',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: AppColors.primaryText,
              ),
            ),
            const SizedBox(height: 16),

            TextFormField(
              controller: _nameController,
              decoration: const InputDecoration(
                labelText: 'اسم المنتج *',
                hintText: 'مثال: نظارة VisionLens الطبية',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.shopping_bag),
              ),
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'يرجى إدخال اسم المنتج';
                }
                if (value.length < 3) {
                  return 'اسم المنتج يجب أن يكون 3 أحرف على الأقل';
                }
                return null;
              },
            ),

            const SizedBox(height: 16),

            TextFormField(
              controller: _descriptionController,
              decoration: const InputDecoration(
                labelText: 'وصف المنتج *',
                hintText: 'اكتب وصفاً مفصلاً للمنتج...',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.description),
              ),
              maxLines: 4,
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'يرجى إدخال وصف المنتج';
                }
                if (value.length < 10) {
                  return 'الوصف يجب أن يكون 10 أحرف على الأقل';
                }
                return null;
              },
            ),

            const SizedBox(height: 16),

            // اختيار الفئة
            _categories.isEmpty
                ? const Center(child: CircularProgressIndicator())
                : DropdownButtonFormField<String>(
                    value: _selectedCategoryId,
                    decoration: const InputDecoration(
                      labelText: 'فئة المنتج *',
                      border: OutlineInputBorder(),
                      prefixIcon: Icon(Icons.category),
                    ),
                    items: _categories.map((category) {
                      return DropdownMenuItem<String>(
                        value: category.id,
                        child: Text(category.name),
                      );
                    }).toList(),
                    onChanged: (value) {
                      setState(() {
                        _selectedCategoryId = value;
                        // تحديد نوع المنتج بناءً على اسم الفئة
                        final selectedCategory = _categories.firstWhere(
                          (cat) => cat.id == value,
                        );
                        _selectedType = _getProductTypeFromCategoryName(selectedCategory.name);
                      });
                      if (kDebugMode) {
                        debugPrint('🔄 تم اختيار الفئة: $value');
                      }
                    },
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'يرجى اختيار فئة المنتج';
                      }
                      return null;
                    },
                  ),
          ],
        ),
      ),
    );
  }

  Widget _buildPriceStockCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'السعر والمخزون',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: AppColors.primaryText,
              ),
            ),
            const SizedBox(height: 16),

            Row(
              children: [
                Expanded(
                  child: TextFormField(
                    controller: _priceController,
                    decoration: const InputDecoration(
                      labelText: 'السعر الحالي (د.ع) *',
                      hintText: '75000',
                      border: OutlineInputBorder(),
                      prefixIcon: Icon(Icons.attach_money),
                    ),
                    keyboardType: TextInputType.number,
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'يرجى إدخال السعر';
                      }
                      if (double.tryParse(value) == null) {
                        return 'يرجى إدخال رقم صحيح';
                      }
                      if (double.parse(value) <= 0) {
                        return 'السعر يجب أن يكون أكبر من صفر';
                      }
                      return null;
                    },
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: TextFormField(
                    controller: _originalPriceController,
                    decoration: const InputDecoration(
                      labelText: 'السعر الأصلي (د.ع)',
                      hintText: '100000',
                      border: OutlineInputBorder(),
                      prefixIcon: Icon(Icons.price_change),
                    ),
                    keyboardType: TextInputType.number,
                    validator: (value) {
                      if (value != null && value.isNotEmpty) {
                        if (double.tryParse(value) == null) {
                          return 'يرجى إدخال رقم صحيح';
                        }
                        final currentPrice = double.tryParse(
                          _priceController.text,
                        );
                        final originalPrice = double.parse(value);
                        if (currentPrice != null &&
                            originalPrice <= currentPrice) {
                          return 'السعر الأصلي يجب أن يكون أكبر من السعر الحالي';
                        }
                      }
                      return null;
                    },
                  ),
                ),
              ],
            ),

            const SizedBox(height: 16),

            TextFormField(
              controller: _stockController,
              decoration: const InputDecoration(
                labelText: 'الكمية المتوفرة *',
                hintText: '50',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.inventory),
              ),
              keyboardType: TextInputType.number,
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'يرجى إدخال الكمية';
                }
                if (int.tryParse(value) == null) {
                  return 'يرجى إدخال رقم صحيح';
                }
                if (int.parse(value) < 0) {
                  return 'الكمية لا يمكن أن تكون سالبة';
                }
                return null;
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildProductDetailsCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'تفاصيل المنتج',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: AppColors.primaryText,
              ),
            ),
            const SizedBox(height: 16),

            Row(
              children: [
                Expanded(
                  child: TextFormField(
                    controller: _brandController,
                    decoration: const InputDecoration(
                      labelText: 'العلامة التجارية',
                      hintText: 'VisionLens',
                      border: OutlineInputBorder(),
                      prefixIcon: Icon(Icons.branding_watermark),
                    ),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: TextFormField(
                    controller: _materialController,
                    decoration: const InputDecoration(
                      labelText: 'المادة',
                      hintText: 'أسيتات',
                      border: OutlineInputBorder(),
                      prefixIcon: Icon(Icons.texture),
                    ),
                  ),
                ),
              ],
            ),

            const SizedBox(height: 16),

            Row(
              children: [
                Expanded(
                  child: TextFormField(
                    controller: _colorController,
                    decoration: const InputDecoration(
                      labelText: 'اللون',
                      hintText: 'أسود',
                      border: OutlineInputBorder(),
                      prefixIcon: Icon(Icons.color_lens),
                    ),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: TextFormField(
                    controller: _sizeController,
                    decoration: const InputDecoration(
                      labelText: 'المقاس',
                      hintText: 'متوسط',
                      border: OutlineInputBorder(),
                      prefixIcon: Icon(Icons.straighten),
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildProductSettingsCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'إعدادات المنتج',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: AppColors.primaryText,
              ),
            ),
            const SizedBox(height: 16),

            SwitchListTile(
              title: const Text('منتج جديد'),
              subtitle: const Text('سيظهر في قسم المنتجات الجديدة'),
              value: _isNew,
              onChanged: (value) {
                setState(() {
                  _isNew = value;
                });
              },
              activeColor: AppColors.primaryColor,
            ),

            SwitchListTile(
              title: const Text('منتج مميز'),
              subtitle: const Text('سيظهر في قسم المنتجات المميزة'),
              value: _isFeatured,
              onChanged: (value) {
                setState(() {
                  _isFeatured = value;
                });
              },
              activeColor: AppColors.primaryColor,
            ),

            SwitchListTile(
              title: const Text('عرض خاص'),
              subtitle: const Text('سيظهر في قسم العروض الخاصة'),
              value: _isOnSale,
              onChanged: (value) {
                setState(() {
                  _isOnSale = value;
                });
              },
              activeColor: AppColors.primaryColor,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSaveButton() {
    return SizedBox(
      width: double.infinity,
      child: ElevatedButton(
        onPressed: _isLoading ? null : _saveProduct,
        style: ElevatedButton.styleFrom(
          backgroundColor: AppColors.primaryColor,
          foregroundColor: AppColors.white,
          padding: const EdgeInsets.symmetric(vertical: 16),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
        ),
        child: _isLoading
            ? const SizedBox(
                height: 20,
                width: 20,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(AppColors.white),
                ),
              )
            : const Text(
                'حفظ المنتج',
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
              ),
      ),
    );
  }

  ProductType _getProductType(String categoryId) {
    switch (categoryId) {
      case 'sunglasses':
        return ProductType.sunglasses;
      case 'contact-lenses':
        return ProductType.contactLenses;
      case 'reading-glasses':
        return ProductType.readingGlasses;
      default:
        return ProductType.eyeglasses;
    }
  }

  ProductType _getProductTypeFromCategoryName(String categoryName) {
    final lowerName = categoryName.toLowerCase();
    if (lowerName.contains('شمس') || lowerName.contains('sun')) {
      return ProductType.sunglasses;
    } else if (lowerName.contains('عدس') || lowerName.contains('contact')) {
      return ProductType.contactLenses;
    } else if (lowerName.contains('قراءة') || lowerName.contains('reading')) {
      return ProductType.readingGlasses;
    } else {
      return ProductType.eyeglasses;
    }
  }

  String _getCategoryName(String categoryId) {
    // البحث عن الفئة في القائمة المحملة
    try {
      final category = _categories.firstWhere((cat) => cat.id == categoryId);
      return category.name;
    } catch (e) {
      // في حالة عدم العثور على الفئة، استخدم القيم الافتراضية
      switch (categoryId) {
        case 'sunglasses':
          return 'نظارات شمسية';
        case 'contact-lenses':
          return 'عدسات لاصقة';
        case 'reading-glasses':
          return 'نظارات قراءة';
        default:
          return 'نظارات طبية';
      }
    }
  }

  Future<void> _saveProduct() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    // التحقق من اختيار الفئة
    if (_selectedCategoryId == null || _categories.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('يرجى اختيار الفئة أولاً'),
          backgroundColor: AppColors.errorColor,
        ),
      );
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      // إنشاء معرف المنتج أولاً
      final productId = 'product_${DateTime.now().millisecondsSinceEpoch}';

      // تحديد رابط الصورة
      String imageUrl = 'assets/images/placeholder_product.jpg';
      List<String> imageUrls = ['assets/images/placeholder_product.jpg'];

      // إذا كان هناك صورة محددة، رفعها إلى Firebase Storage
      if (_selectedImageBytes != null) {
        if (kDebugMode) {
          debugPrint('🔄 رفع الصورة إلى Firebase Storage...');
          debugPrint('📏 حجم الصورة الأصلي: ${_selectedImageBytes!.length} بايت');
        }

        // رفع الصورة إلى Firebase Storage بجودتها الأصلية
        final uploadedUrl = await FirebaseStorageService.uploadProductImage(
          productId,
          _selectedImageBytes!,
          _selectedImageName ?? 'product_image.jpg',
        );

        if (uploadedUrl != null) {
          imageUrl = uploadedUrl;
          imageUrls = [uploadedUrl];
          if (kDebugMode) {
            debugPrint('✅ تم رفع الصورة إلى Firebase Storage بنجاح');
            debugPrint('🔗 رابط الصورة: $imageUrl');
          }
        } else {
          throw Exception('فشل في رفع الصورة إلى Firebase Storage');
        }
      }

      final product = Product(
        id: productId,
        name: _nameController.text,
        description: _descriptionController.text,
        price: double.parse(_priceController.text),
        originalPrice: _originalPriceController.text.isNotEmpty
            ? double.parse(_originalPriceController.text)
            : null,
        image: imageUrl,
        images: imageUrls,
        categoryId: _selectedCategoryId!,
        categoryName: _getCategoryName(_selectedCategoryId!),
        rating: 0.0,
        reviewsCount: 0,
        inStock: int.parse(_stockController.text) > 0,
        stockQuantity: int.parse(_stockController.text),
        specifications: {
          'العلامة التجارية': _brandController.text.isNotEmpty
              ? _brandController.text
              : 'VisionLens',
          'المادة': _materialController.text.isNotEmpty
              ? _materialController.text
              : 'غير محدد',
          'اللون': _colorController.text.isNotEmpty
              ? _colorController.text
              : 'غير محدد',
          'المقاس': _sizeController.text.isNotEmpty
              ? _sizeController.text
              : 'غير محدد',
        },
        type: _selectedType,
        brand: _brandController.text.isNotEmpty
            ? _brandController.text
            : 'VisionLens',
        material: _materialController.text.isNotEmpty
            ? _materialController.text
            : 'غير محدد',
        color: _colorController.text.isNotEmpty
            ? _colorController.text
            : 'غير محدد',
        size: _sizeController.text.isNotEmpty
            ? _sizeController.text
            : 'غير محدد',
        availableColors: _colorController.text.isNotEmpty
            ? [_colorController.text]
            : ['أسود'],
        availableSizes: _sizeController.text.isNotEmpty
            ? [_sizeController.text]
            : ['متوسط'],
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        isNew: _isNew,
        isFeatured: _isFeatured,
        isOnSale: _isOnSale,
        discountPercentage:
            _isOnSale && _originalPriceController.text.isNotEmpty
            ? ((double.parse(_originalPriceController.text) -
                      double.parse(_priceController.text)) /
                  double.parse(_originalPriceController.text) *
                  100)
            : null,
      );

      // حفظ المنتج في Firebase
      await FirestoreDataService.addProduct(product);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم حفظ المنتج بنجاح'),
            backgroundColor: AppColors.successColor,
          ),
        );
        Navigator.of(context).pop(true);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في حفظ المنتج: $e'),
            backgroundColor: AppColors.errorColor,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  /// بناء قسم صورة المنتج
  Widget _buildImageCard() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.image, color: AppColors.primaryColor),
                const SizedBox(width: 8),
                Text(
                  'صورة المنتج',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: AppColors.primaryColor,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),

            // عرض الصورة المختارة أو زر الاختيار
            if (_selectedImageBytes != null) ...[
              Container(
                width: double.infinity,
                height: 200,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: AppColors.primaryColor.withOpacity(0.3)),
                ),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(8),
                  child: Image.memory(
                    _selectedImageBytes!,
                    fit: BoxFit.cover,
                  ),
                ),
              ),
              const SizedBox(height: 8),
              Text(
                'الملف: ${_selectedImageName ?? "غير معروف"}',
                style: TextStyle(
                  fontSize: 12,
                  color: AppColors.textSecondary,
                ),
              ),
              const SizedBox(height: 8),
              Row(
                children: [
                  Expanded(
                    child: ElevatedButton.icon(
                      onPressed: _pickImage,
                      icon: const Icon(Icons.edit),
                      label: const Text('تغيير الصورة'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: AppColors.primaryColor,
                        foregroundColor: AppColors.white,
                      ),
                    ),
                  ),
                  const SizedBox(width: 8),
                  ElevatedButton.icon(
                    onPressed: () {
                      setState(() {
                        _selectedImageBytes = null;
                        _selectedImageName = null;
                      });
                    },
                    icon: const Icon(Icons.delete),
                    label: const Text('حذف'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppColors.errorColor,
                      foregroundColor: AppColors.white,
                    ),
                  ),
                ],
              ),
            ] else ...[
              Container(
                width: double.infinity,
                height: 200,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(
                    color: AppColors.primaryColor.withOpacity(0.3),
                    style: BorderStyle.dashed,
                  ),
                ),
                child: InkWell(
                  onTap: _pickImage,
                  borderRadius: BorderRadius.circular(8),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.cloud_upload,
                        size: 48,
                        color: AppColors.primaryColor.withOpacity(0.6),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'اضغط لاختيار صورة',
                        style: TextStyle(
                          fontSize: 16,
                          color: AppColors.primaryColor,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        'PNG, JPG, JPEG (أقل من 5MB)',
                        style: TextStyle(
                          fontSize: 12,
                          color: AppColors.textSecondary,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  @override
  void dispose() {
    _nameController.dispose();
    _descriptionController.dispose();
    _priceController.dispose();
    _originalPriceController.dispose();
    _stockController.dispose();
    _brandController.dispose();
    _materialController.dispose();
    _colorController.dispose();
    _sizeController.dispose();
    super.dispose();
  }
}
