# 👓 VisionLens - تطبيق متجر النظارات والعدسات

تطبيق Flutter متكامل لبيع النظارات والعدسات اللاصقة مع نظام إدارة شامل.

## ✨ المميزات الرئيسية

### 🛍️ للعملاء
- **تصفح المنتجات**: نظارات طبية، شمسية، وعدسات لاصقة
- **البحث والفلترة**: بحث متقدم حسب النوع والسعر والعلامة التجارية
- **سلة التسوق**: إضافة وإدارة المنتجات
- **نظام الدفع**: دفع آمن ومتعدد الطرق
- **تتبع الطلبات**: متابعة حالة الطلب من الطلب حتى التسليم
- **التقييمات**: تقييم المنتجات ومشاركة التجارب
- **الملف الشخصي**: إدارة البيانات والعناوين

### 🔐 نظام المصادقة
- **تسجيل الدخول**: بالإيميل أو Google Sign-In
- **إنشاء حساب**: تسجيل سريع وآمن
- **استعادة كلمة المرور**: عبر البريد الإلكتروني

### 📱 إدارة المتجر
- **لوحة تحكم الإدارة**: إدارة شاملة للمتجر
- **إدارة المنتجات**: إضافة وتعديل وحذف المنتجات
- **إدارة الطلبات**: متابعة ومعالجة الطلبات
- **إدارة العملاء**: عرض بيانات العملاء
- **التقارير**: إحصائيات المبيعات والأداء

## 🚀 التشغيل

### المتطلبات
- Flutter SDK
- Android Studio / VS Code
- Firebase Project

### خطوات التشغيل
```bash
# تحميل التبعيات
flutter pub get

# تشغيل على Android
flutter run

# بناء APK
flutter build apk
```

## 🛠️ التقنيات المستخدمة

- **Flutter**: إطار العمل الرئيسي
- **Firebase**: قاعدة البيانات والمصادقة
- **Provider**: إدارة الحالة
- **Google Maps**: تحديد المواقع
- **Image Picker**: رفع الصور
- **Local Notifications**: الإشعارات المحلية

## 📁 هيكل المشروع

```
lib/
├── models/          # نماذج البيانات
├── screens/         # شاشات التطبيق
├── services/        # خدمات Firebase والAPI
├── main.dart        # نقطة البداية
└── firebase_options.dart  # إعدادات Firebase
```

## 📞 الدعم

للدعم الفني أو الاستفسارات، يرجى التواصل معنا.
