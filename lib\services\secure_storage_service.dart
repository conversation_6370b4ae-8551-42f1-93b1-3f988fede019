import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/user_simple.dart' as user_model;
import '../models/cart_item.dart';
import '../models/wishlist_item.dart';
import '../models/order.dart';
import '../models/payment_method.dart' as payment_model;
import 'encryption_service.dart';

/// خدمة التخزين الآمن - تشفر جميع البيانات الحساسة
class SecureStorageService {
  static const String _keyPrefix = 'visionlens_secure_';
  static bool _isInitialized = false;
  
  // مفاتيح التخزين الآمن
  static const String _userKey = 'current_user';
  static const String _cartKey = 'cart_items';
  static const String _wishlistKey = 'wishlist_items';
  static const String _ordersKey = 'orders';
  static const String _paymentMethodsKey = 'payment_methods';
  static const String _authTokenKey = 'auth_token';
  static const String _adminEmailsKey = 'admin_emails';
  static const String _appSettingsKey = 'app_settings';
  
  /// تهيئة الخدمة
  static Future<void> initialize() async {
    try {
      if (_isInitialized) return;
      
      if (kDebugMode) {
        print('🔐 تهيئة خدمة التخزين الآمن...');
      }
      
      // تهيئة خدمة التشفير أولاً
      await EncryptionService.initialize();
      
      _isInitialized = true;
      
      if (kDebugMode) {
        print('✅ تم تهيئة خدمة التخزين الآمن بنجاح');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في تهيئة خدمة التخزين الآمن: $e');
      }
      rethrow;
    }
  }
  
  /// حفظ بيانات مشفرة
  static Future<void> _saveEncrypted(String key, dynamic data) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final jsonString = jsonEncode(data);
      final encryptedData = EncryptionService.encryptSensitiveData(jsonString);
      await prefs.setString('$_keyPrefix$key', encryptedData);
      
      if (kDebugMode) {
        print('🔐 تم حفظ البيانات المشفرة: $key');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في حفظ البيانات المشفرة: $e');
      }
      rethrow;
    }
  }
  
  /// قراءة بيانات مشفرة
  static Future<dynamic> _loadEncrypted(String key) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final encryptedData = prefs.getString('$_keyPrefix$key');
      
      if (encryptedData == null) return null;
      
      final jsonString = EncryptionService.decryptSensitiveData(encryptedData);
      final data = jsonDecode(jsonString);
      
      if (kDebugMode) {
        print('🔓 تم قراءة البيانات المشفرة: $key');
      }
      
      return data;
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في قراءة البيانات المشفرة: $e');
      }
      return null;
    }
  }
  
  /// حذف بيانات مشفرة
  static Future<void> _deleteEncrypted(String key) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove('$_keyPrefix$key');
      
      if (kDebugMode) {
        print('🗑️ تم حذف البيانات المشفرة: $key');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في حذف البيانات المشفرة: $e');
      }
    }
  }
  
  // ==================== إدارة المستخدمين الآمنة ====================
  
  /// حفظ بيانات المستخدم مشفرة
  static Future<void> saveUser(user_model.User user) async {
    try {
      if (!_isInitialized) await initialize();
      
      // تشفير البيانات الحساسة فقط
      final userData = user.toJson();
      
      // تشفير البيانات الحساسة
      if (userData['phone'] != null) {
        userData['phone'] = EncryptionService.encryptSensitiveData(userData['phone']);
      }
      
      await _saveEncrypted(_userKey, userData);
      
      if (kDebugMode) {
        print('🔐 تم حفظ بيانات المستخدم مشفرة');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في حفظ بيانات المستخدم: $e');
      }
      rethrow;
    }
  }
  
  /// تحميل بيانات المستخدم وفك تشفيرها
  static Future<user_model.User?> loadUser() async {
    try {
      if (!_isInitialized) await initialize();
      
      final userData = await _loadEncrypted(_userKey);
      if (userData == null) return null;
      
      // فك تشفير البيانات الحساسة
      if (userData['phone'] != null && userData['phone'].toString().contains(':')) {
        try {
          userData['phone'] = EncryptionService.decryptSensitiveData(userData['phone']);
        } catch (e) {
          if (kDebugMode) {
            print('⚠️ فشل في فك تشفير رقم الهاتف، قد يكون غير مشفر');
          }
        }
      }
      
      return user_model.User.fromJson(userData);
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في تحميل بيانات المستخدم: $e');
      }
      return null;
    }
  }
  
  /// مسح بيانات المستخدم
  static Future<void> clearUser() async {
    try {
      await _deleteEncrypted(_userKey);
      
      if (kDebugMode) {
        print('🗑️ تم مسح بيانات المستخدم الآمنة');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في مسح بيانات المستخدم: $e');
      }
    }
  }
  
  // ==================== إدارة السلة الآمنة ====================
  
  /// حفظ السلة مشفرة
  static Future<void> saveCart(List<CartItem> cartItems) async {
    try {
      if (!_isInitialized) await initialize();
      
      final cartJson = cartItems.map((item) => item.toJson()).toList();
      await _saveEncrypted(_cartKey, cartJson);
      
      if (kDebugMode) {
        print('🔐 تم حفظ السلة مشفرة (${cartItems.length} عنصر)');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في حفظ السلة: $e');
      }
    }
  }
  
  /// تحميل السلة وفك تشفيرها
  static Future<List<CartItem>> loadCart() async {
    try {
      if (!_isInitialized) await initialize();
      
      final cartData = await _loadEncrypted(_cartKey);
      if (cartData == null) return [];
      
      final cartList = cartData as List;
      return cartList.map((item) => CartItem.fromJson(item)).toList();
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في تحميل السلة: $e');
      }
      return [];
    }
  }
  
  // ==================== إدارة طرق الدفع الآمنة ====================
  
  /// حفظ طرق الدفع مشفرة
  static Future<void> savePaymentMethods(List<payment_model.PaymentMethod> methods) async {
    try {
      if (!_isInitialized) await initialize();
      
      final methodsJson = methods.map((method) => method.toJson()).toList();
      await _saveEncrypted(_paymentMethodsKey, methodsJson);
      
      if (kDebugMode) {
        print('🔐 تم حفظ طرق الدفع مشفرة (${methods.length} طريقة)');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في حفظ طرق الدفع: $e');
      }
    }
  }
  
  /// تحميل طرق الدفع وفك تشفيرها
  static Future<List<payment_model.PaymentMethod>> loadPaymentMethods() async {
    try {
      if (!_isInitialized) await initialize();
      
      final methodsData = await _loadEncrypted(_paymentMethodsKey);
      if (methodsData == null) return [];
      
      final methodsList = methodsData as List;
      return methodsList.map((method) => payment_model.PaymentMethod.fromJson(method)).toList();
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في تحميل طرق الدفع: $e');
      }
      return [];
    }
  }
  
  // ==================== إدارة التوكن الآمن ====================
  
  /// حفظ توكن المصادقة مشفر
  static Future<void> saveAuthToken(String token) async {
    try {
      if (!_isInitialized) await initialize();
      
      final prefs = await SharedPreferences.getInstance();
      final encryptedToken = EncryptionService.encryptSensitiveData(token);
      await prefs.setString('$_keyPrefix$_authTokenKey', encryptedToken);
      
      if (kDebugMode) {
        print('🔐 تم حفظ توكن المصادقة مشفر');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في حفظ توكن المصادقة: $e');
      }
    }
  }
  
  /// تحميل توكن المصادقة وفك تشفيره
  static Future<String?> loadAuthToken() async {
    try {
      if (!_isInitialized) await initialize();
      
      final prefs = await SharedPreferences.getInstance();
      final encryptedToken = prefs.getString('$_keyPrefix$_authTokenKey');
      
      if (encryptedToken == null) return null;
      
      return EncryptionService.decryptSensitiveData(encryptedToken);
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في تحميل توكن المصادقة: $e');
      }
      return null;
    }
  }
  
  /// مسح توكن المصادقة
  static Future<void> clearAuthToken() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove('$_keyPrefix$_authTokenKey');
      
      if (kDebugMode) {
        print('🗑️ تم مسح توكن المصادقة');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في مسح توكن المصادقة: $e');
      }
    }
  }
  
  // ==================== مسح جميع البيانات الآمنة ====================
  
  /// مسح جميع البيانات المشفرة
  static Future<void> clearAllSecureData() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final keys = prefs.getKeys().where((key) => key.startsWith(_keyPrefix));
      
      for (final key in keys) {
        await prefs.remove(key);
      }
      
      // تنظيف الذاكرة
      EncryptionService.clearSensitiveData();
      
      if (kDebugMode) {
        print('🧹 تم مسح جميع البيانات الآمنة');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في مسح البيانات الآمنة: $e');
      }
    }
  }
  
  // ==================== التحقق من سلامة البيانات ====================
  
  /// التحقق من سلامة البيانات المشفرة
  static Future<bool> verifyDataIntegrity() async {
    try {
      if (!_isInitialized) await initialize();
      
      final prefs = await SharedPreferences.getInstance();
      final keys = prefs.getKeys().where((key) => key.startsWith(_keyPrefix));
      
      int validCount = 0;
      int totalCount = keys.length;
      
      for (final key in keys) {
        try {
          final encryptedData = prefs.getString(key);
          if (encryptedData != null) {
            // محاولة فك التشفير للتحقق من السلامة
            EncryptionService.decryptSensitiveData(encryptedData);
            validCount++;
          }
        } catch (e) {
          if (kDebugMode) {
            print('⚠️ بيانات تالفة في المفتاح: $key');
          }
        }
      }
      
      final isValid = validCount == totalCount;
      
      if (kDebugMode) {
        print('🔍 فحص سلامة البيانات: $validCount/$totalCount صحيح');
        print(isValid ? '✅ جميع البيانات سليمة' : '⚠️ بعض البيانات تالفة');
      }
      
      return isValid;
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في فحص سلامة البيانات: $e');
      }
      return false;
    }
  }
  
  // ==================== ترحيل البيانات القديمة ====================
  
  /// ترحيل البيانات القديمة غير المشفرة إلى النظام الآمن
  static Future<void> migrateOldData() async {
    try {
      if (!_isInitialized) await initialize();
      
      if (kDebugMode) {
        print('🔄 بدء ترحيل البيانات القديمة...');
      }
      
      final prefs = await SharedPreferences.getInstance();
      int migratedCount = 0;
      
      // ترحيل بيانات المستخدم
      final oldUserData = prefs.getString('current_user');
      if (oldUserData != null && !oldUserData.contains(':')) {
        try {
          final userData = jsonDecode(oldUserData);
          await _saveEncrypted(_userKey, userData);
          await prefs.remove('current_user');
          migratedCount++;
          
          if (kDebugMode) {
            print('✅ تم ترحيل بيانات المستخدم');
          }
        } catch (e) {
          if (kDebugMode) {
            print('⚠️ فشل في ترحيل بيانات المستخدم: $e');
          }
        }
      }
      
      // ترحيل بيانات السلة
      final oldCartData = prefs.getString('cart_items');
      if (oldCartData != null && !oldCartData.contains(':')) {
        try {
          final cartData = jsonDecode(oldCartData);
          await _saveEncrypted(_cartKey, cartData);
          await prefs.remove('cart_items');
          migratedCount++;
          
          if (kDebugMode) {
            print('✅ تم ترحيل بيانات السلة');
          }
        } catch (e) {
          if (kDebugMode) {
            print('⚠️ فشل في ترحيل بيانات السلة: $e');
          }
        }
      }
      
      // ترحيل بيانات المفضلة
      final oldWishlistData = prefs.getString('wishlist_items');
      if (oldWishlistData != null && !oldWishlistData.contains(':')) {
        try {
          final wishlistData = jsonDecode(oldWishlistData);
          await _saveEncrypted(_wishlistKey, wishlistData);
          await prefs.remove('wishlist_items');
          migratedCount++;
          
          if (kDebugMode) {
            print('✅ تم ترحيل بيانات المفضلة');
          }
        } catch (e) {
          if (kDebugMode) {
            print('⚠️ فشل في ترحيل بيانات المفضلة: $e');
          }
        }
      }
      
      if (kDebugMode) {
        print('🎉 تم ترحيل $migratedCount عنصر إلى النظام الآمن');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في ترحيل البيانات القديمة: $e');
      }
    }
  }
  
  // ==================== إحصائيات الأمان ====================
  
  /// الحصول على إحصائيات الأمان
  static Future<Map<String, dynamic>> getSecurityStats() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final secureKeys = prefs.getKeys().where((key) => key.startsWith(_keyPrefix));
      final oldKeys = prefs.getKeys().where((key) => 
        !key.startsWith(_keyPrefix) && 
        ['current_user', 'cart_items', 'wishlist_items'].contains(key)
      );
      
      return {
        'encryptedDataCount': secureKeys.length,
        'unencryptedDataCount': oldKeys.length,
        'isFullySecure': oldKeys.isEmpty,
        'lastSecurityCheck': DateTime.now().toIso8601String(),
      };
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في جلب إحصائيات الأمان: $e');
      }
      return {};
    }
  }
}
