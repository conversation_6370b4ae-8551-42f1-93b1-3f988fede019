import 'package:flutter/material.dart';
import '../../app_properties.dart';
import '../../services/app_state.dart';

class AppRatingPage extends StatefulWidget {
  const AppRatingPage({super.key});

  @override
  State<AppRatingPage> createState() => _AppRatingPageState();
}

class _AppRatingPageState extends State<AppRatingPage> {
  final TextEditingController _commentController = TextEditingController();
  final AppState _appState = AppState();
  
  int _selectedRating = 0;
  String _selectedAspect = '';
  bool _isSubmitting = false;
  
  final List<RatingAspect> _aspects = [
    RatingAspect(
      title: 'سهولة الاستخدام',
      icon: Icons.touch_app,
      description: 'مدى سهولة التنقل في التطبيق',
    ),
    RatingAspect(
      title: 'جودة المنتجات',
      icon: Icons.high_quality,
      description: 'رضاك عن جودة النظارات والعدسات',
    ),
    RatingAspect(
      title: 'سرعة التوصيل',
      icon: Icons.delivery_dining,
      description: 'مدى سرعة وصول طلباتك',
    ),
    RatingAspect(
      title: 'خدمة العملاء',
      icon: Icons.support_agent,
      description: 'جودة الدعم والمساعدة',
    ),
    RatingAspect(
      title: 'التطبيق عموماً',
      icon: Icons.star,
      description: 'تقييمك الشامل للتطبيق',
    ),
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.backgroundColor,
      appBar: AppBar(
        backgroundColor: AppColors.primaryColor,
        foregroundColor: AppColors.white,
        title: const Row(
          children: [
            Icon(Icons.star_rate, size: 24),
            SizedBox(width: 8),
            Text('تقييم التطبيق'),
          ],
        ),
        elevation: 0,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // رسالة ترحيبية
            _buildWelcomeCard(),
            
            const SizedBox(height: 24),
            
            // اختيار جانب التقييم
            _buildAspectSelection(),
            
            const SizedBox(height: 24),
            
            // تقييم النجوم
            if (_selectedAspect.isNotEmpty) ...[
              _buildStarRating(),
              const SizedBox(height: 24),
            ],
            
            // تعليق اختياري
            if (_selectedRating > 0) ...[
              _buildCommentSection(),
              const SizedBox(height: 24),
            ],
            
            // زر الإرسال
            if (_selectedRating > 0) _buildSubmitButton(),
          ],
        ),
      ),
    );
  }

  Widget _buildWelcomeCard() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            AppColors.primaryColor,
            AppColors.primaryColor.withValues(alpha: 0.8),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16),
      ),
      child: Column(
        children: [
          const Icon(
            Icons.favorite,
            color: AppColors.white,
            size: 48,
          ),
          const SizedBox(height: 12),
          const Text(
            'نحن نقدر رأيك!',
            style: TextStyle(
              color: AppColors.white,
              fontSize: 20,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'ساعدنا في تحسين تجربتك من خلال تقييم التطبيق',
            textAlign: TextAlign.center,
            style: TextStyle(
              color: AppColors.white.withValues(alpha: 0.9),
              fontSize: 14,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAspectSelection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'ما الذي تريد تقييمه؟',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 12),
        ...(_aspects.map((aspect) => _buildAspectCard(aspect))),
      ],
    );
  }

  Widget _buildAspectCard(RatingAspect aspect) {
    final isSelected = _selectedAspect == aspect.title;
    
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      child: InkWell(
        onTap: () {
          setState(() {
            _selectedAspect = aspect.title;
            _selectedRating = 0; // إعادة تعيين التقييم
          });
        },
        borderRadius: BorderRadius.circular(12),
        child: Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: isSelected ? AppColors.primaryColor.withValues(alpha: 0.1) : AppColors.white,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: isSelected ? AppColors.primaryColor : AppColors.lightGrey,
              width: isSelected ? 2 : 1,
            ),
          ),
          child: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: isSelected 
                      ? AppColors.primaryColor 
                      : AppColors.primaryColor.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(10),
                ),
                child: Icon(
                  aspect.icon,
                  color: isSelected ? AppColors.white : AppColors.primaryColor,
                  size: 24,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      aspect.title,
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        color: isSelected ? AppColors.primaryColor : AppColors.textColor,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      aspect.description,
                      style: TextStyle(
                        fontSize: 12,
                        color: AppColors.textColor.withValues(alpha: 0.6),
                      ),
                    ),
                  ],
                ),
              ),
              if (isSelected)
                const Icon(
                  Icons.check_circle,
                  color: AppColors.primaryColor,
                ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildStarRating() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: AppColors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: AppColors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          Text(
            'كيف تقيم $_selectedAspect؟',
            style: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 20),
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: List.generate(5, (index) {
              final starNumber = index + 1;
              return GestureDetector(
                onTap: () {
                  setState(() {
                    _selectedRating = starNumber;
                  });
                },
                child: Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 4),
                  child: Icon(
                    starNumber <= _selectedRating ? Icons.star : Icons.star_border,
                    color: starNumber <= _selectedRating 
                        ? AppColors.warningColor 
                        : AppColors.lightGrey,
                    size: 40,
                  ),
                ),
              );
            }),
          ),
          const SizedBox(height: 12),
          Text(
            _getRatingText(_selectedRating),
            style: TextStyle(
              fontSize: 14,
              color: AppColors.textColor.withValues(alpha: 0.7),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCommentSection() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: AppColors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: AppColors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'أخبرنا المزيد (اختياري)',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 12),
          TextField(
            controller: _commentController,
            maxLines: 4,
            decoration: InputDecoration(
              hintText: 'شاركنا تجربتك أو اقتراحاتك لتحسين الخدمة...',
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(color: AppColors.lightGrey),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(color: AppColors.primaryColor),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSubmitButton() {
    return SizedBox(
      width: double.infinity,
      child: ElevatedButton(
        onPressed: _isSubmitting ? null : _submitRating,
        style: ElevatedButton.styleFrom(
          backgroundColor: AppColors.primaryColor,
          foregroundColor: AppColors.white,
          padding: const EdgeInsets.symmetric(vertical: 16),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
        ),
        child: _isSubmitting
            ? const SizedBox(
                height: 20,
                width: 20,
                child: CircularProgressIndicator(
                  color: AppColors.white,
                  strokeWidth: 2,
                ),
              )
            : const Text(
                'إرسال التقييم',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
      ),
    );
  }

  String _getRatingText(int rating) {
    switch (rating) {
      case 1:
        return 'سيء جداً 😞';
      case 2:
        return 'سيء 😕';
      case 3:
        return 'مقبول 😐';
      case 4:
        return 'جيد 😊';
      case 5:
        return 'ممتاز! 🤩';
      default:
        return '';
    }
  }

  Future<void> _submitRating() async {
    setState(() {
      _isSubmitting = true;
    });

    try {
      // محاكاة إرسال التقييم
      await Future.delayed(const Duration(seconds: 2));
      
      if (mounted) {
        // إظهار رسالة نجاح
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: const Row(
              children: [
                Icon(Icons.check_circle, color: AppColors.white),
                SizedBox(width: 8),
                Text('شكراً لك! تم إرسال تقييمك بنجاح'),
              ],
            ),
            backgroundColor: AppColors.success,
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(10),
            ),
          ),
        );
        
        // العودة للصفحة السابقة
        Navigator.pop(context);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: const Row(
              children: [
                Icon(Icons.error, color: AppColors.white),
                SizedBox(width: 8),
                Text('حدث خطأ، يرجى المحاولة مرة أخرى'),
              ],
            ),
            backgroundColor: AppColors.errorColor,
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(10),
            ),
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isSubmitting = false;
        });
      }
    }
  }

  @override
  void dispose() {
    _commentController.dispose();
    super.dispose();
  }
}

class RatingAspect {
  final String title;
  final IconData icon;
  final String description;

  RatingAspect({
    required this.title,
    required this.icon,
    required this.description,
  });
}
