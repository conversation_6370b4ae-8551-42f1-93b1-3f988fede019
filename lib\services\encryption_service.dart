import 'dart:convert';
import 'dart:math';
import 'dart:typed_data';
import 'package:crypto/crypto.dart';
import 'package:encrypt/encrypt.dart' as encrypt;
import 'package:bcrypt/bcrypt.dart';
import 'package:flutter/foundation.dart';

/// خدمة التشفير الآمنة للتطبيق
/// تدعم تشفير كلمات المرور والبيانات الحساسة
class EncryptionService {
  static late encrypt.Encrypter _encrypter;
  static late encrypt.IV _iv;
  static bool _isInitialized = false;
  
  // مفتاح التشفير الرئيسي (يجب تغييره في الإنتاج)
  static const String _masterKey = 'VisionLens2025SecureKey123456789';
  
  /// تهيئة خدمة التشفير
  static Future<void> initialize() async {
    try {
      if (_isInitialized) return;
      
      if (kDebugMode) {
        print('🔐 تهيئة خدمة التشفير...');
      }
      
      // إنشاء مفتاح التشفير
      final key = encrypt.Key.fromBase64(_generateBase64Key(_masterKey));
      _iv = encrypt.IV.fromSecureRandom(16);
      _encrypter = encrypt.Encrypter(encrypt.AES(key));
      
      _isInitialized = true;
      
      if (kDebugMode) {
        print('✅ تم تهيئة خدمة التشفير بنجاح');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في تهيئة خدمة التشفير: $e');
      }
      rethrow;
    }
  }
  
  /// توليد مفتاح Base64 من نص
  static String _generateBase64Key(String input) {
    final bytes = utf8.encode(input);
    final hash = sha256.convert(bytes);
    return base64.encode(hash.bytes);
  }
  
  // ==================== تشفير كلمات المرور ====================
  
  /// تشفير كلمة المرور باستخدام bcrypt
  static String hashPassword(String password) {
    try {
      if (kDebugMode) {
        print('🔐 تشفير كلمة المرور...');
      }
      
      // استخدام bcrypt مع salt قوي
      final hashedPassword = BCrypt.hashpw(password, BCrypt.gensalt());
      
      if (kDebugMode) {
        print('✅ تم تشفير كلمة المرور بنجاح');
      }
      
      return hashedPassword;
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في تشفير كلمة المرور: $e');
      }
      rethrow;
    }
  }
  
  /// التحقق من كلمة المرور
  static bool verifyPassword(String password, String hashedPassword) {
    try {
      return BCrypt.checkpw(password, hashedPassword);
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في التحقق من كلمة المرور: $e');
      }
      return false;
    }
  }
  
  // ==================== تشفير البيانات الحساسة ====================
  
  /// تشفير البيانات الحساسة
  static String encryptSensitiveData(String data) {
    try {
      if (!_isInitialized) {
        throw Exception('خدمة التشفير غير مُهيأة');
      }
      
      final encrypted = _encrypter.encrypt(data, iv: _iv);
      
      // دمج IV مع البيانات المشفرة
      final combined = '${_iv.base64}:${encrypted.base64}';
      
      if (kDebugMode) {
        print('🔐 تم تشفير البيانات الحساسة');
      }
      
      return combined;
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في تشفير البيانات: $e');
      }
      rethrow;
    }
  }
  
  /// فك تشفير البيانات الحساسة
  static String decryptSensitiveData(String encryptedData) {
    try {
      if (!_isInitialized) {
        throw Exception('خدمة التشفير غير مُهيأة');
      }
      
      // فصل IV عن البيانات المشفرة
      final parts = encryptedData.split(':');
      if (parts.length != 2) {
        throw Exception('تنسيق البيانات المشفرة غير صحيح');
      }
      
      final iv = encrypt.IV.fromBase64(parts[0]);
      final encrypted = encrypt.Encrypted.fromBase64(parts[1]);
      
      final decrypted = _encrypter.decrypt(encrypted, iv: iv);
      
      if (kDebugMode) {
        print('🔓 تم فك تشفير البيانات الحساسة');
      }
      
      return decrypted;
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في فك تشفير البيانات: $e');
      }
      rethrow;
    }
  }
  
  // ==================== تشفير بيانات الدفع ====================
  
  /// تشفير بيانات البطاقة الائتمانية
  static Map<String, String> encryptCardData({
    required String cardNumber,
    required String expiryDate,
    required String cvv,
    required String holderName,
  }) {
    try {
      if (kDebugMode) {
        print('🔐 تشفير بيانات البطاقة...');
      }
      
      return {
        'cardNumber': encryptSensitiveData(cardNumber),
        'expiryDate': encryptSensitiveData(expiryDate),
        'cvv': encryptSensitiveData(cvv),
        'holderName': encryptSensitiveData(holderName),
        'encryptedAt': DateTime.now().toIso8601String(),
      };
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في تشفير بيانات البطاقة: $e');
      }
      rethrow;
    }
  }
  
  /// فك تشفير بيانات البطاقة الائتمانية
  static Map<String, String> decryptCardData(Map<String, String> encryptedData) {
    try {
      if (kDebugMode) {
        print('🔓 فك تشفير بيانات البطاقة...');
      }
      
      return {
        'cardNumber': decryptSensitiveData(encryptedData['cardNumber']!),
        'expiryDate': decryptSensitiveData(encryptedData['expiryDate']!),
        'cvv': decryptSensitiveData(encryptedData['cvv']!),
        'holderName': decryptSensitiveData(encryptedData['holderName']!),
      };
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في فك تشفير بيانات البطاقة: $e');
      }
      rethrow;
    }
  }
  
  // ==================== توليد المفاتيح الآمنة ====================
  
  /// توليد مفتاح عشوائي آمن
  static String generateSecureKey({int length = 32}) {
    final random = Random.secure();
    final bytes = List<int>.generate(length, (i) => random.nextInt(256));
    return base64.encode(bytes);
  }
  
  /// توليد salt عشوائي
  static String generateSalt() {
    return BCrypt.gensalt();
  }
  
  // ==================== تشفير JSON ====================
  
  /// تشفير كائن JSON
  static String encryptJson(Map<String, dynamic> data) {
    try {
      final jsonString = jsonEncode(data);
      return encryptSensitiveData(jsonString);
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في تشفير JSON: $e');
      }
      rethrow;
    }
  }
  
  /// فك تشفير كائن JSON
  static Map<String, dynamic> decryptJson(String encryptedData) {
    try {
      final jsonString = decryptSensitiveData(encryptedData);
      return jsonDecode(jsonString);
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في فك تشفير JSON: $e');
      }
      rethrow;
    }
  }
  
  // ==================== تشفير الملفات ====================
  
  /// تشفير ملف
  static Uint8List encryptFile(Uint8List fileBytes) {
    try {
      if (!_isInitialized) {
        throw Exception('خدمة التشفير غير مُهيأة');
      }
      
      final encrypted = _encrypter.encryptBytes(fileBytes, iv: _iv);
      
      // دمج IV مع البيانات المشفرة
      final ivBytes = _iv.bytes;
      final encryptedBytes = encrypted.bytes;
      
      final combined = Uint8List(ivBytes.length + encryptedBytes.length);
      combined.setRange(0, ivBytes.length, ivBytes);
      combined.setRange(ivBytes.length, combined.length, encryptedBytes);
      
      return combined;
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في تشفير الملف: $e');
      }
      rethrow;
    }
  }
  
  /// فك تشفير ملف
  static Uint8List decryptFile(Uint8List encryptedBytes) {
    try {
      if (!_isInitialized) {
        throw Exception('خدمة التشفير غير مُهيأة');
      }
      
      // فصل IV عن البيانات المشفرة
      final ivBytes = encryptedBytes.sublist(0, 16);
      final dataBytes = encryptedBytes.sublist(16);
      
      final iv = encrypt.IV(ivBytes);
      final encrypted = encrypt.Encrypted(dataBytes);
      
      final decrypted = _encrypter.decryptBytes(encrypted, iv: iv);
      
      return Uint8List.fromList(decrypted);
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في فك تشفير الملف: $e');
      }
      rethrow;
    }
  }
  
  // ==================== أدوات مساعدة ====================
  
  /// التحقق من قوة كلمة المرور
  static bool isPasswordStrong(String password) {
    // كلمة المرور يجب أن تحتوي على:
    // - 8 أحرف على الأقل
    // - حرف كبير واحد على الأقل
    // - حرف صغير واحد على الأقل
    // - رقم واحد على الأقل
    // - رمز خاص واحد على الأقل
    
    if (password.length < 8) return false;
    
    final hasUppercase = password.contains(RegExp(r'[A-Z]'));
    final hasLowercase = password.contains(RegExp(r'[a-z]'));
    final hasDigits = password.contains(RegExp(r'[0-9]'));
    final hasSpecialCharacters = password.contains(RegExp(r'[!@#$%^&*(),.?":{}|<>]'));
    
    return hasUppercase && hasLowercase && hasDigits && hasSpecialCharacters;
  }
  
  /// تنظيف الذاكرة (إزالة البيانات الحساسة)
  static void clearSensitiveData() {
    try {
      // إعادة تعيين المتغيرات الحساسة
      _iv = encrypt.IV.fromSecureRandom(16);
      
      if (kDebugMode) {
        print('🧹 تم تنظيف البيانات الحساسة من الذاكرة');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في تنظيف البيانات الحساسة: $e');
      }
    }
  }
}
